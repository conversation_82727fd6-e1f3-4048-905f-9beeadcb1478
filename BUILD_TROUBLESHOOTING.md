# Build Troubleshooting Guide

## Quick Fix for Your Current Issue

Based on the error you encountered, here's the immediate solution:

### 1. Run the Fix Script
```bash
python fix_build_issues.py
```

### 2. Try the Simple Build
```bash
python nuitka_simple_build.py
```

### 3. Or Use the Batch Script
```batch
build_comprehensive.bat
# Choose option 7: Simple Build
```

## Error Analysis

Your build failed with:
```
TypeError: stat: path should be string, bytes, os.PathLike or integer, not NoneType
```

**Root Cause:** <PERSON><PERSON><PERSON> is trying to access a path that is `None`, likely due to:
1. Empty directories being included (`keys`, `instant_loading`)
2. Too many packages being included, some of which don't exist
3. Deprecated Nuitka options

## Solutions Applied

### 1. **Fixed Empty Directory Issue**
- The build script now checks if directories have actual files before including them
- Empty directories are automatically excluded

### 2. **Updated Nuitka Options**
- Changed `--disable-console` to `--windows-console-mode=disable`
- Removed redundant plugin declarations

### 3. **Filtered Problematic Packages**
- Excluded packages that don't exist or cause issues: `kivy`, `flask`, `matplotlib`, etc.
- Added existence checks before including packages

### 4. **Created Simple Build Script**
- `nuitka_simple_build.py` uses only essential packages
- Minimal dependencies to avoid complex issues

## Build Options (Recommended Order)

### Option 1: Simple Build (Recommended for troubleshooting)
```bash
python nuitka_simple_build.py
```
- Uses only essential packages
- Avoids complex dependency issues
- Fastest build time

### Option 2: Fixed Comprehensive Build
```bash
python nuitka_comprehensive_build.py --mode release
```
- Uses the fixed comprehensive script
- Better dependency detection
- More features but potentially more issues

### Option 3: Manual Nuitka Command
```bash
python -m nuitka --standalone --onefile --output-filename=WOWBingoGame --windows-console-mode=disable --include-data-dir=assets=assets --include-data-dir=data=data --include-package=pygame --include-package=pyperclip --include-package=json --include-package=datetime --include-package=sqlite3 main.py
```

## Common Issues and Solutions

### Issue: "Nuitka not found"
**Solution:**
```bash
pip install nuitka
```

### Issue: "Missing packages"
**Solution:**
```bash
pip install pygame pyperclip
# Or use minimal requirements:
pip install -r minimal_requirements.txt
```

### Issue: "Build too slow"
**Solution:**
- Use simple build: `python nuitka_simple_build.py`
- Add `--jobs=1` to reduce CPU usage
- Close other applications

### Issue: "Executable too large"
**Solution:**
- Use release mode (not debug)
- Remove unused Python files from project
- Use simple build script

### Issue: "Audio not working in executable"
**Solution:**
- Verify all audio files exist in `assets/` directories
- Check file permissions
- Ensure MP3 files are not corrupted

### Issue: "Import errors in executable"
**Solution:**
- Add missing packages to the include list
- Check for relative imports
- Verify all local modules are included

## Debug Steps

### 1. Check Prerequisites
```bash
python -c "import pygame, pyperclip; print('All packages available')"
python -m nuitka --version
```

### 2. Test Simple Build
```bash
python nuitka_simple_build.py --debug
```

### 3. Check Project Structure
```bash
python -c "
import os
print('Assets:', os.path.exists('assets'))
print('Data:', os.path.exists('data'))
print('Main:', os.path.exists('main.py'))
"
```

### 4. Clean and Retry
```bash
python fix_build_issues.py
python nuitka_simple_build.py --clean
```

## Advanced Troubleshooting

### Enable Verbose Output
```bash
python nuitka_simple_build.py --debug
```

### Check Nuitka Crash Report
If build fails, check `nuitka-crash-report.xml` for detailed error information.

### Manual Package Testing
Test individual packages:
```bash
python -c "import pygame; print('pygame OK')"
python -c "import pyperclip; print('pyperclip OK')"
```

### Memory Issues
If you get memory errors:
- Close other applications
- Use `--jobs=1` to reduce parallel compilation
- Try building on a machine with more RAM

## Platform-Specific Issues

### Windows
- Install Visual Studio Build Tools for better performance
- Ensure Windows SDK is available
- Use PowerShell or Command Prompt (not Git Bash)

### Linux
- Install build-essential: `sudo apt install build-essential`
- Ensure Python development headers: `sudo apt install python3-dev`

### macOS
- Install Xcode command line tools: `xcode-select --install`
- Ensure latest Python version

## Success Indicators

A successful build will:
1. Complete without errors
2. Create an executable in `dist/` directory
3. Executable size between 50-200 MB (depending on mode)
4. Executable runs without Python installation

## Getting Help

If you still have issues:

1. **Run the fix script:** `python fix_build_issues.py`
2. **Try simple build:** `python nuitka_simple_build.py`
3. **Check the crash report:** Look for `nuitka-crash-report.xml`
4. **Provide error details:** Include the full error message and command used

## Quick Commands Summary

```bash
# Fix common issues
python fix_build_issues.py

# Simple build (recommended)
python nuitka_simple_build.py

# Clean simple build
python nuitka_simple_build.py --clean

# Debug build
python nuitka_simple_build.py --debug

# Interactive menu
build_comprehensive.bat  # Windows
./build_comprehensive.sh # Linux/macOS
```
