@echo off
:: ================================================================
:: WOW Bingo Game - Comprehensive Build Script (Windows)
:: ================================================================
:: This script provides an easy-to-use interface for building the
:: WOW Bingo Game using the comprehensive Nuitka build system.
:: ================================================================

setlocal EnableDelayedExpansion
cd /d "%~dp0"

:: Configuration
set "PYTHON_SCRIPT=nuitka_comprehensive_build.py"
set "PROJECT_NAME=WOW Bingo Game"

echo ================================================================
echo     %PROJECT_NAME% - Comprehensive Build System
echo ================================================================
echo.

:: Check if Python script exists
if not exist "%PYTHON_SCRIPT%" (
    echo Error: %PYTHON_SCRIPT% not found!
    echo Please ensure the comprehensive build script is in the same directory.
    pause
    exit /b 1
)

:: Check Python installation
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found!
    echo Please install Python 3.7 or higher and add it to your PATH.
    pause
    exit /b 1
)

:: Display menu
:menu
echo.
echo ================================================================
echo                    BUILD OPTIONS
echo ================================================================
echo.
echo 1. Quick Release Build (Recommended)
echo 2. Debug Build (with debugging symbols)
echo 3. Optimized Build (maximum performance)
echo 4. Clean Release Build (clean + build)
echo 5. Test Build (build + test executable)
echo 6. Full Build (clean + optimized + test)
echo 7. Simple Build (minimal dependencies - if others fail)
echo 8. Minimal Build (ultra-minimal to avoid Nuitka bugs)
echo 9. PyInstaller Build (alternative to Nuitka)
echo 10. Show Help
echo 11. Exit
echo.
set /p "choice=Please select an option (1-11): "

if "%choice%"=="1" goto quick_release
if "%choice%"=="2" goto debug_build
if "%choice%"=="3" goto optimized_build
if "%choice%"=="4" goto clean_release
if "%choice%"=="5" goto test_build
if "%choice%"=="6" goto full_build
if "%choice%"=="7" goto simple_build
if "%choice%"=="8" goto minimal_build
if "%choice%"=="9" goto pyinstaller_build
if "%choice%"=="10" goto show_help
if "%choice%"=="11" goto exit
echo Invalid choice. Please try again.
goto menu

:quick_release
echo.
echo Starting Quick Release Build...
python "%PYTHON_SCRIPT%" --mode release
goto build_complete

:debug_build
echo.
echo Starting Debug Build...
python "%PYTHON_SCRIPT%" --mode debug --verbose
goto build_complete

:optimized_build
echo.
echo Starting Optimized Build...
python "%PYTHON_SCRIPT%" --mode optimized
goto build_complete

:clean_release
echo.
echo Starting Clean Release Build...
python "%PYTHON_SCRIPT%" --mode release --clean
goto build_complete

:test_build
echo.
echo Starting Test Build...
python "%PYTHON_SCRIPT%" --mode release --test
goto build_complete

:full_build
echo.
echo Starting Full Build (this may take a while)...
python "%PYTHON_SCRIPT%" --mode optimized --clean --test --verbose
goto build_complete

:simple_build
echo.
echo Starting Simple Build (minimal dependencies)...
echo This build uses only essential packages to avoid dependency issues.
python nuitka_simple_build.py
goto build_complete

:minimal_build
echo.
echo Starting Minimal Build (ultra-minimal)...
echo This build uses the absolute minimum options to avoid Nuitka bugs.
echo Some features may not work if required packages are missing.
python nuitka_minimal_build.py
goto build_complete

:pyinstaller_build
echo.
echo Starting PyInstaller Build (alternative to Nuitka)...
echo This uses PyInstaller instead of Nuitka as an alternative approach.
python pyinstaller_build.py --clean
goto build_complete

:show_help
echo.
echo ================================================================
echo                        HELP
echo ================================================================
echo.
echo Build Modes:
echo   Release    - Standard build with good performance
echo   Debug      - Build with debugging symbols (larger file)
echo   Optimized  - Maximum performance build (takes longer)
echo   Simple     - Minimal dependencies (use if others fail)
echo   Minimal    - Ultra-minimal to avoid Nuitka bugs
echo   PyInstaller- Alternative build tool (if Nuitka fails)
echo.
echo Options:
echo   Clean      - Remove previous build files before building
echo   Test       - Test the executable after building
echo   Verbose    - Show detailed build output
echo.
echo Output:
echo   The executable will be created in the 'dist' directory
echo   A build report will be generated with detailed information
echo.
echo Requirements:
echo   - Python 3.7 or higher
echo   - Nuitka (will be installed automatically if missing)
echo   - Required Python packages (pygame, pyperclip)
echo.
pause
goto menu

:build_complete
echo.
if %errorlevel% equ 0 (
    echo ================================================================
    echo                   BUILD SUCCESSFUL!
    echo ================================================================
    echo.
    echo The executable has been created in the 'dist' directory.
    echo Check the build report for detailed information.
    echo.
    set /p "open_choice=Do you want to open the dist directory? (y/n): "
    if /i "!open_choice!"=="y" (
        if exist "dist" (
            explorer "dist"
        ) else (
            echo Dist directory not found.
        )
    )
) else (
    echo ================================================================
    echo                   BUILD FAILED!
    echo ================================================================
    echo.
    echo Please check the error messages above for details.
    echo You may need to install missing dependencies or fix configuration issues.
)
echo.
pause
goto menu

:exit
echo.
echo Thank you for using the %PROJECT_NAME% build system!
echo.
pause
exit /b 0
