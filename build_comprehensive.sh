#!/bin/bash
# ================================================================
# WOW Bingo Game - Comprehensive Build Script (Linux/macOS)
# ================================================================
# This script provides an easy-to-use interface for building the
# WOW Bingo Game using the comprehensive Nuitka build system.
# ================================================================

set -e  # Exit on any error

# Configuration
PYTHON_SCRIPT="nuitka_comprehensive_build.py"
PROJECT_NAME="WOW Bingo Game"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    echo -e "${1}${2}${NC}"
}

# Function to print header
print_header() {
    echo "================================================================"
    echo "     $1"
    echo "================================================================"
}

# Function to check prerequisites
check_prerequisites() {
    print_color $BLUE "Checking prerequisites..."
    
    # Check if Python script exists
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_color $RED "Error: $PYTHON_SCRIPT not found!"
        echo "Please ensure the comprehensive build script is in the same directory."
        exit 1
    fi
    
    # Check Python installation
    if ! command -v python3 &> /dev/null; then
        if ! command -v python &> /dev/null; then
            print_color $RED "Error: Python not found!"
            echo "Please install Python 3.7 or higher."
            exit 1
        else
            PYTHON_CMD="python"
        fi
    else
        PYTHON_CMD="python3"
    fi
    
    # Check Python version
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    print_color $GREEN "Found Python $PYTHON_VERSION"
    
    echo "Prerequisites check completed."
}

# Function to show menu
show_menu() {
    echo
    print_header "BUILD OPTIONS"
    echo
    echo "1. Quick Release Build (Recommended)"
    echo "2. Debug Build (with debugging symbols)"
    echo "3. Optimized Build (maximum performance)"
    echo "4. Clean Release Build (clean + build)"
    echo "5. Test Build (build + test executable)"
    echo "6. Full Build (clean + optimized + test)"
    echo "7. Show Help"
    echo "8. Exit"
    echo
}

# Function to show help
show_help() {
    echo
    print_header "HELP"
    echo
    echo "Build Modes:"
    echo "  Release    - Standard build with good performance"
    echo "  Debug      - Build with debugging symbols (larger file)"
    echo "  Optimized  - Maximum performance build (takes longer)"
    echo
    echo "Options:"
    echo "  Clean      - Remove previous build files before building"
    echo "  Test       - Test the executable after building"
    echo "  Verbose    - Show detailed build output"
    echo
    echo "Output:"
    echo "  The executable will be created in the 'dist' directory"
    echo "  A build report will be generated with detailed information"
    echo
    echo "Requirements:"
    echo "  - Python 3.7 or higher"
    echo "  - Nuitka (will be installed automatically if missing)"
    echo "  - Required Python packages (pygame, pyperclip)"
    echo
}

# Function to run build
run_build() {
    local build_args="$1"
    local build_name="$2"
    
    echo
    print_color $BLUE "Starting $build_name..."
    
    if $PYTHON_CMD "$PYTHON_SCRIPT" $build_args; then
        echo
        print_header "BUILD SUCCESSFUL!"
        echo
        print_color $GREEN "The executable has been created in the 'dist' directory."
        print_color $GREEN "Check the build report for detailed information."
        echo
        
        # Ask if user wants to open the dist directory
        read -p "Do you want to open the dist directory? (y/n): " open_choice
        if [[ $open_choice =~ ^[Yy]$ ]]; then
            if [ -d "dist" ]; then
                if command -v xdg-open &> /dev/null; then
                    xdg-open dist
                elif command -v open &> /dev/null; then
                    open dist
                else
                    print_color $YELLOW "Please manually navigate to the 'dist' directory."
                fi
            else
                print_color $RED "Dist directory not found."
            fi
        fi
    else
        echo
        print_header "BUILD FAILED!"
        echo
        print_color $RED "Please check the error messages above for details."
        print_color $RED "You may need to install missing dependencies or fix configuration issues."
    fi
}

# Main script
main() {
    print_header "$PROJECT_NAME - Comprehensive Build System"
    echo
    
    check_prerequisites
    
    while true; do
        show_menu
        read -p "Please select an option (1-8): " choice
        
        case $choice in
            1)
                run_build "--mode release" "Quick Release Build"
                ;;
            2)
                run_build "--mode debug --verbose" "Debug Build"
                ;;
            3)
                run_build "--mode optimized" "Optimized Build"
                ;;
            4)
                run_build "--mode release --clean" "Clean Release Build"
                ;;
            5)
                run_build "--mode release --test" "Test Build"
                ;;
            6)
                run_build "--mode optimized --clean --test --verbose" "Full Build (this may take a while)"
                ;;
            7)
                show_help
                read -p "Press Enter to continue..."
                ;;
            8)
                echo
                print_color $GREEN "Thank you for using the $PROJECT_NAME build system!"
                echo
                exit 0
                ;;
            *)
                print_color $RED "Invalid choice. Please try again."
                ;;
        esac
        
        echo
        read -p "Press Enter to return to menu..."
    done
}

# Run main function
main "$@"
