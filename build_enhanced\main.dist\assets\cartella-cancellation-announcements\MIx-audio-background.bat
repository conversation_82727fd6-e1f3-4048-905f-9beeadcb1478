@echo off
REM Create Output directory if it doesn't exist
if not exist Output mkdir Output

REM Loop through all mp3 files in current directory (excluding background)
for %%f in (*.mp3) do (
    if not "%%f"=="background.mp3" (
        echo Mixing %%f with background music...
        ffmpeg -y -i "%%f" -i "background\background.mp3" -filter_complex "[0:a][1:a]amix=inputs=2:duration=first:dropout_transition=2" "Output\%%~nf.mp3"
    )
)
echo Done!
pause