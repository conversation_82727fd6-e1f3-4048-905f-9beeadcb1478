2025-05-27 13:10:06,264 - INFO - Loaded 0 items from cache
2025-05-27 13:10:06,266 - INFO - Started background data loading
2025-05-27 13:10:06,267 - INFO - OptimizedStatsLoader initialized
2025-05-27 13:10:06,267 - INFO - Using optimized stats loader for integration
2025-05-27 13:10:06,269 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:10:06,269 - INFO - Loaded summary data
2025-05-27 13:10:06,270 - INFO - Loaded game history page 0 (0 records)
2025-05-27 13:10:06,270 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:10:06,271 - INFO - Saved 7 items to cache
2025-05-27 13:10:06,271 - INFO - Background data loading completed
2025-05-27 13:10:06,273 - INFO - Database schema initialized successfully
2025-05-27 13:10:06,277 - INFO - Database schema initialized successfully
2025-05-27 13:10:06,279 - INFO - Stats database initialized successfully
2025-05-27 13:10:06,279 - INFO - Game stats integration module available
2025-05-27 13:10:06,280 - INFO - Started stats event worker thread
2025-05-27 13:10:06,280 - INFO - Stats event hooks initialized
2025-05-27 13:14:27,583 - INFO - Loaded 7 items from cache
2025-05-27 13:14:27,584 - INFO - Started background data loading
2025-05-27 13:14:27,585 - INFO - OptimizedStatsLoader initialized
2025-05-27 13:14:27,585 - INFO - Using optimized stats loader for integration
2025-05-27 13:14:27,588 - INFO - Loaded weekly stats for 7 days
2025-05-27 13:14:27,589 - INFO - Loaded summary data
2025-05-27 13:14:27,589 - INFO - Loaded game history page 0 (0 records)
2025-05-27 13:14:27,590 - INFO - Loaded game history metadata (total pages: 1)
2025-05-27 13:14:27,591 - INFO - Saved 7 items to cache
2025-05-27 13:14:27,591 - INFO - Background data loading completed
2025-05-27 13:14:27,593 - INFO - Database schema initialized successfully
2025-05-27 13:14:27,596 - INFO - Database schema initialized successfully
2025-05-27 13:14:27,597 - INFO - Stats database initialized successfully
2025-05-27 13:14:27,597 - INFO - Game stats integration module available
2025-05-27 13:14:27,597 - INFO - Started stats event worker thread
2025-05-27 13:14:27,598 - INFO - Stats event hooks initialized
2025-05-27 13:56:08,422 - INFO - Connected to RethinkDB at localhost:28015
2025-05-27 13:56:08,681 - INFO - Database security initialized successfully
2025-05-27 13:56:08,684 - INFO - DB Operation: {"timestamp": "2025-05-27 13:56:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:56:08,685 - INFO - Created secure database connection
2025-05-27 13:56:08,761 - INFO - Database schema initialized successfully
2025-05-27 13:56:08,763 - INFO - DB Operation: {"timestamp": "2025-05-27 13:56:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:56:08,765 - INFO - DB Operation: {"timestamp": "2025-05-27 13:56:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:56:08,766 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 13:56:08,777 - INFO - Loaded sync metadata: {'daily_stats': 1748379240.7115402, 'game_history': 1748379240.7115402, 'wallet_transactions': 1748379240.7115402, 'admin_users': 1748379240.7115402, 'audit_log': 1748379240.7115402}
2025-05-27 13:56:08,915 - INFO - Sync thread started
2025-05-27 13:56:08,920 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:56:08,922 - INFO - DB Operation: {"timestamp": "2025-05-27 13:56:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:56:08,925 - ERROR - Error getting remote changes: 'RethinkDBManager' object has no attribute 'query'
2025-05-27 13:56:08,928 - WARNING - Connection validation failed, creating new connection
2025-05-27 13:56:08,929 - INFO - DB Operation: {"timestamp": "2025-05-27 13:56:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 13:56:08,933 - INFO - Syncing 1 local changes to remote for table game_history
2025-05-27 13:56:08,933 - ERROR - Error syncing change to remote: 'RethinkDBManager' object has no attribute 'get'
2025-05-27 13:56:08,934 - ERROR - Error getting remote changes: 'RethinkDBManager' object has no attribute 'query'
2025-05-27 13:56:08,937 - ERROR - Error getting remote changes: 'RethinkDBManager' object has no attribute 'query'
2025-05-27 13:56:08,940 - ERROR - Error getting remote changes: 'RethinkDBManager' object has no attribute 'query'
2025-05-27 13:56:08,943 - ERROR - Error getting remote changes: 'RethinkDBManager' object has no attribute 'query'
2025-05-27 13:56:08,945 - INFO - Saved sync metadata: {'daily_stats': 1748379368.9187026, 'game_history': 1748379368.9187026, 'wallet_transactions': 1748379368.9187026, 'admin_users': 1748379368.9187026, 'audit_log': 1748379368.9187026}
2025-05-27 13:56:08,947 - INFO - Sync manager initialized (RethinkDB available: True)
2025-05-27 13:56:09,188 - INFO - Database schema initialized successfully
2025-05-27 13:56:09,192 - INFO - RECORDING GAME COMPLETED IN THREAD_SAFE_DB
2025-05-27 13:56:09,193 - INFO - Game data: {'winner_name': 'SyncTest', 'winner_cartella': 99, 'claim_type': 'Test', 'game_duration': 60, 'player_count': 1, 'prize_amount': 100, 'commission_percentage': 10, 'called_numbers': [1, 2, 3, 4, 5], 'is_demo_mode': False}
2025-05-27 13:56:09,680 - INFO - Game statistics recorded in database (ID: 136)
2025-05-27 13:56:09,682 - INFO - Game winner recorded via thread_safe_db.py - Winner: SyncTest, Status: Won
