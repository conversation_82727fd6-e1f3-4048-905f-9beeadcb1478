2025-05-27T12:18:15.557152200 0.029520s notice: In recursion: removing file 'D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data\\tmp'
2025-05-27T12:18:15.559229300 0.031595s warn: Trying to delete non-existent file 'D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data\\tmp'
2025-05-27T12:18:15.562699400 0.035064s notice: Initializing directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:18:15.564444600 0.036809s info: Creating a default database for your convenience. (This is because you ran 'rethinkdb' without 'create', 'serve', or '--join', and the directory 'D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data' did not already exist or is empty.)
2025-05-27T12:18:15.564523600 0.036887s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T12:18:15.566737800 0.039103s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T12:18:15.566817100 0.039180s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:18:15.880563500 0.352928s info: Automatically using cache size of 2775 MB
2025-05-27T12:18:15.896478500 0.368842s notice: Listening for intracluster connections on port 29015
2025-05-27T12:18:15.899547500 0.371911s notice: Listening for client driver connections on port 28015
2025-05-27T12:18:15.899690000 0.372054s warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025-05-27T12:18:16.012355100 0.484719s error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
2025-05-27T12:23:48.496018700 0.043409s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T12:23:48.496190300 0.043578s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T12:23:48.496217200 0.043604s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:23:48.599275000 0.146662s info: Automatically using cache size of 2640 MB
2025-05-27T12:23:48.604517100 0.151905s notice: Listening for intracluster connections on port 29015
2025-05-27T12:23:48.608025300 0.155413s notice: Listening for client driver connections on port 28015
2025-05-27T12:23:48.608230900 0.155618s warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025-05-27T12:23:48.711977800 0.259365s error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
2025-05-27T12:29:49.538883400 0.037879s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T12:29:49.539048400 0.038041s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T12:29:49.539078500 0.038070s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:29:49.627032500 0.126025s info: Automatically using cache size of 2658 MB
2025-05-27T12:29:49.632315700 0.131308s notice: Listening for intracluster connections on port 29015
2025-05-27T12:29:49.634162100 0.133154s notice: Listening for client driver connections on port 28015
2025-05-27T12:29:49.634262900 0.133255s warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025-05-27T12:29:49.746014200 0.245007s error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
2025-05-27T12:35:12.713467700 0.027013s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T12:35:12.713601600 0.027144s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T12:35:12.713620600 0.027162s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:35:12.819318800 0.132861s info: Automatically using cache size of 2897 MB
2025-05-27T12:35:12.822800600 0.136343s notice: Listening for intracluster connections on port 29015
2025-05-27T12:35:12.824206900 0.137749s notice: Listening for client driver connections on port 28015
2025-05-27T12:35:12.824296400 0.137838s warn: bind failed: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025-05-27T12:35:13.044383200 0.357926s error: Could not bind to http port: The address at localhost:8080 is reserved or already in use.
2025-05-27T12:45:45.970356400 0.030801s error: --daemon not implemented on windows
2025-05-27T12:47:58.669863200 0.025519s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T12:47:58.670039700 0.025693s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T12:47:58.670065500 0.025718s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:47:58.752025500 0.107678s info: Automatically using cache size of 2867 MB
2025-05-27T12:47:58.757664700 0.113319s notice: Listening for intracluster connections on port 29015
2025-05-27T12:47:58.760843700 0.116496s notice: Listening for client driver connections on port 28015
2025-05-27T12:47:58.760958700 0.116611s notice: Listening for administrative HTTP connections on port 8081
2025-05-27T12:47:58.760961900 0.116615s notice: Listening on cluster address: 127.0.0.1
2025-05-27T12:47:58.760963400 0.116616s notice: Listening on driver address: 127.0.0.1
2025-05-27T12:47:58.760964900 0.116617s notice: Listening on http address: 127.0.0.1
2025-05-27T12:47:58.760966500 0.116619s notice: To fully expose RethinkDB on the network, bind to all addresses by running rethinkdb with the `--bind all` command line option.
2025-05-27T12:47:58.760968000 0.116621s notice: Server ready, "DESKTOP_HD6UHPP_8n3" 227a5a3a-25cc-4a65-833a-8ae8b20b38f7
2025-05-27T12:49:07.228666500 0.034570s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T12:49:07.228843200 0.034744s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T12:49:07.228906000 0.034807s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T12:49:07.360266500 0.166168s info: Automatically using cache size of 2689 MB
2025-05-27T12:49:07.365264400 0.171166s notice: Listening for intracluster connections on port 29015
2025-05-27T12:49:07.366935700 0.172837s notice: Listening for client driver connections on port 28015
2025-05-27T12:49:07.367062100 0.172963s notice: Listening for administrative HTTP connections on port 8081
2025-05-27T12:49:07.367068500 0.172970s notice: Listening on cluster address: 127.0.0.1
2025-05-27T12:49:07.367071400 0.172972s notice: Listening on driver address: 127.0.0.1
2025-05-27T12:49:07.367073700 0.172974s notice: Listening on http address: 127.0.0.1
2025-05-27T12:49:07.367075900 0.172977s notice: To fully expose RethinkDB on the network, bind to all addresses by running rethinkdb with the `--bind all` command line option.
2025-05-27T12:49:07.367080600 0.172981s notice: Server ready, "DESKTOP_HD6UHPP_8n3" 227a5a3a-25cc-4a65-833a-8ae8b20b38f7
2025-05-27T12:49:53.595872100 46.401773s info: Table a88e8e20-e3bb-4230-8a04-0018f34584b4: Starting a new Raft election for term 1.
2025-05-27T12:49:53.596368300 46.402269s info: Table a88e8e20-e3bb-4230-8a04-0018f34584b4: Added replica on this server.
2025-05-27T12:49:53.652200600 46.458102s info: Table a88e8e20-e3bb-4230-8a04-0018f34584b4: This server is Raft leader for term 1. Latest log index is 0.
2025-05-27T13:20:22.060407900 0.033552s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T13:20:22.060565800 0.033708s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T13:20:22.060594500 0.033736s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T13:20:22.153670400 0.126812s info: Automatically using cache size of 2611 MB
2025-05-27T13:20:22.158088000 0.131230s notice: Listening for intracluster connections on port 29015
2025-05-27T13:20:22.167427900 0.140570s notice: Listening for client driver connections on port 28015
2025-05-27T13:20:22.167641500 0.140783s notice: Listening for administrative HTTP connections on port 8081
2025-05-27T13:20:22.167643500 0.140785s notice: Listening on cluster address: 127.0.0.1
2025-05-27T13:20:22.167644400 0.140786s notice: Listening on driver address: 127.0.0.1
2025-05-27T13:20:22.167645600 0.140787s notice: Listening on http address: 127.0.0.1
2025-05-27T13:20:22.167652600 0.140794s notice: To fully expose RethinkDB on the network, bind to all addresses by running rethinkdb with the `--bind all` command line option.
2025-05-27T13:20:22.167653600 0.140795s notice: Server ready, "DESKTOP_HD6UHPP_8n3" 227a5a3a-25cc-4a65-833a-8ae8b20b38f7
2025-05-27T13:20:23.240395900 1.213538s info: Table a88e8e20-e3bb-4230-8a04-0018f34584b4: Starting a new Raft election for term 2.
2025-05-27T13:20:23.304730300 1.277872s info: Table a88e8e20-e3bb-4230-8a04-0018f34584b4: This server is Raft leader for term 2. Latest log index is 3.
2025-05-27T13:20:36.902839100 14.875981s info: Table 93e45ced-99c5-4009-bc4f-162ed8963779: Starting a new Raft election for term 1.
2025-05-27T13:20:36.903476700 14.876618s info: Table 93e45ced-99c5-4009-bc4f-162ed8963779: Added replica on this server.
2025-05-27T13:20:36.957702600 14.930844s info: Table 93e45ced-99c5-4009-bc4f-162ed8963779: This server is Raft leader for term 1. Latest log index is 0.
2025-05-27T13:20:39.196758400 17.169901s info: Table 93e45ced-99c5-4009-bc4f-162ed8963779: Configuration is changing.
2025-05-27T13:20:39.309165400 17.282307s info: Table 93e45ced-99c5-4009-bc4f-162ed8963779: Configuration is changing.
2025-05-27T13:21:04.677613700 0.026993s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T13:21:04.677740700 0.027117s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T13:21:04.677759000 0.027135s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T13:21:04.785448400 0.134825s info: Automatically using cache size of 2666 MB
2025-05-27T13:21:04.791446500 0.140823s notice: Listening for intracluster connections on port 29015
2025-05-27T13:21:04.802041300 0.151418s error: Error in thread 0 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:21:04.801493300 0.150870s error: Error in thread 1 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:21:04.802072000 0.151448s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:21:04.801705100 0.151081s error: Error in thread 3 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:21:04.802084700 0.151461s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:21:04.802095500 0.151472s error: Backtrace:
2025-05-27T13:21:04.801886000 0.151262s error: Error in thread 2 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:21:04.802119700 0.151496s error: Backtrace:
2025-05-27T13:21:04.802116700 0.151493s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:21:04.802130100 0.151506s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:21:04.802143300 0.151519s error: Backtrace:
2025-05-27T13:21:04.802152500 0.151528s error: Backtrace:
2025-05-27T13:21:04.845520600 0.194898s error: Tue May 27 13:21:04 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8CDE [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:21:04.845926500 0.195304s error: Exiting.
2025-05-27T13:21:04.846084500 0.195461s error: Tue May 27 13:21:04 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8B86 [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:21:04.846141300 0.195517s error: Exiting.
2025-05-27T13:21:04.846264100 0.195640s error: Tue May 27 13:21:04 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8B86 [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:21:04.846313200 0.195689s error: Exiting.
2025-05-27T13:23:05.358497400 0.029323s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T13:23:05.358639600 0.029462s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T13:23:05.358659500 0.029482s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T13:23:05.483971800 0.154794s info: Automatically using cache size of 2658 MB
2025-05-27T13:23:05.487402300 0.158225s notice: Listening for intracluster connections on port 29015
2025-05-27T13:23:05.501461600 0.172284s error: Error in thread 1 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:23:05.501496700 0.172319s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:23:05.501598900 0.172421s error: Backtrace:
2025-05-27T13:23:05.501967500 0.172790s error: Error in thread 3 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:23:05.502336500 0.173159s error: Error in thread 0 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:23:05.502185400 0.173008s error: Error in thread 2 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:23:05.502364000 0.173186s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:23:05.502361100 0.173184s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:23:05.502390100 0.173212s error: Backtrace:
2025-05-27T13:23:05.502395500 0.173218s error: Backtrace:
2025-05-27T13:23:05.502394200 0.173217s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:23:05.502425200 0.173248s error: Backtrace:
2025-05-27T13:23:05.502534700 0.173357s error: Tue May 27 13:23:05 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8B86 [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:23:05.502537100 0.173359s error: Tue May 27 13:23:05 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8CDE [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:23:05.502552200 0.173375s error: Tue May 27 13:23:05 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8B86 [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:23:05.502679200 0.173502s error: Exiting.
2025-05-27T13:23:05.502651100 0.173474s error: Exiting.
2025-05-27T13:23:05.502698100 0.173521s error: Exiting.
2025-05-27T13:24:01.486228700 0.023335s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T13:24:01.486410000 0.023514s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T13:24:01.486438900 0.023542s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T13:24:01.979168300 0.516272s info: Automatically using cache size of 2478 MB
2025-05-27T13:24:01.981737800 0.518841s notice: Listening for intracluster connections on port 29015
2025-05-27T13:24:01.990272700 0.527377s error: Error in thread 1 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:24:01.990825200 0.527929s error: Error in thread 0 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:24:01.990497600 0.527601s error: Error in thread 3 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:24:01.990671900 0.527776s error: Error in thread 2 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:24:01.990859900 0.527964s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:24:01.990966100 0.528070s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:24:01.990980300 0.528084s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:24:01.990994800 0.528098s error: Backtrace:
2025-05-27T13:24:01.991005400 0.528109s error: Backtrace:
2025-05-27T13:24:01.991010200 0.528114s error: Backtrace:
2025-05-27T13:24:01.990870100 0.527974s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:24:01.991210900 0.528315s error: Tue May 27 13:24:01 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8CDE [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:24:01.991234800 0.528339s error: Backtrace:
2025-05-27T13:24:01.991315700 0.528419s error: Exiting.
2025-05-27T13:24:01.991218400 0.528322s error: Tue May 27 13:24:01 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8CDE [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:24:01.991365000 0.528469s error: Exiting.
2025-05-27T13:24:01.991471700 0.528576s error: Tue May 27 13:24:01 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8B86 [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:24:01.991518500 0.528622s error: Exiting.
2025-05-27T13:25:30.854861700 0.021084s notice: Running rethinkdb 2.4.2-11-g6613dc (Windows) (MSC 191627048)...
2025-05-27T13:25:30.854996900 0.021216s notice: Running on 10.0.22631 (Windows 10, Server 2016)
2025-05-27T13:25:30.855016800 0.021235s notice: Loading data from directory D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\data\\rethinkdb_data
2025-05-27T13:25:31.004500000 0.170719s info: Automatically using cache size of 2557 MB
2025-05-27T13:25:31.010386800 0.176606s notice: Listening for intracluster connections on port 29015
2025-05-27T13:25:31.017733300 0.183953s error: Error in thread 1 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:25:31.017995600 0.184215s error: Error in thread 3 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:25:31.018041400 0.184260s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:25:31.018366500 0.184586s error: Error in thread 0 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:25:31.018057700 0.184277s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:25:31.018215900 0.184435s error: Error in thread 2 in c:\\cygwin64\\home\\sam\\rethinkdb\\src\\containers\\binary_blob.hpp at line 39:
2025-05-27T13:25:31.018509700 0.184729s error: Backtrace:
2025-05-27T13:25:31.018391200 0.184610s error: Backtrace:
2025-05-27T13:25:31.018407400 0.184626s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:25:31.018545700 0.184765s error: Guarantee failed: [blob.size() == sizeof(obj_t)] blob.size() = 0, sizeof(obj_t) = 24
2025-05-27T13:25:31.018579100 0.184798s error: Backtrace:
2025-05-27T13:25:31.018582300 0.184801s error: Backtrace:
2025-05-27T13:25:31.018784500 0.185003s error: Tue May 27 13:25:31 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8CDE [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:25:31.018782900 0.185002s error: Tue May 27 13:25:31 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8B86 [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:25:31.018888900 0.185108s error: Exiting.
2025-05-27T13:25:31.018784200 0.185003s error: Tue May 27 13:25:31 2025\n\n1: 0x00007FF64E3DFCDF [The handle is invalid.]\n2: 0x00007FF64E3E0B0A [The handle is invalid.]\n3: 0x00007FF64E7E70B1 [The handle is invalid.]\n4: 0x00007FF64E69BE3C [The handle is invalid.]\n5: 0x00007FF64E6998F3 [The handle is invalid.]\n6: 0x00007FF64E69BAE8 [The handle is invalid.]\n7: 0x00007FF64E6996BD [The handle is invalid.]\n8: 0x00007FF64E69E145 [The handle is invalid.]\n9: 0x00007FF64E7516FF [The handle is invalid.]\n10: 0x00007FF64E7524C3 [The handle is invalid.]\n11: 0x00007FF64E3D8CDE [The handle is invalid.]\n12: 0x00007FF64E3D6CC5 [The handle is invalid.]\n13: 0x00007FF9DE8A594B [The handle is invalid.]\n14: 0x00007FF9E14182E4 [The handle is invalid.]
2025-05-27T13:25:31.018914500 0.185134s error: Exiting.
2025-05-27T13:25:31.018922700 0.185142s error: Exiting.
