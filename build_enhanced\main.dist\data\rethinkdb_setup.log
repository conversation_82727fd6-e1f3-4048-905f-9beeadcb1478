2025-05-27 12:48:22,381 - ERROR - Connection test failed: Could not connect to localhost:28015. Error: [WinError 10061] No connection could be made because the target machine actively refused it
2025-05-27 12:49:55,928 - ERROR - Error initializing database: Index name conflict: `date` is the name of the primary key in:
r.table('daily_stats').index_create('date')
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-05-27 12:50:42,761 - ERROR - Connection test failed: 'DefaultConnection' object has no attribute 'server_info'
2025-05-27 12:51:08,256 - INFO - Database security initialized successfully
2025-05-27 12:51:08,257 - INFO - DB Operation: {"timestamp": "2025-05-27 12:51:08", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:51:08,257 - INFO - Created secure database connection
2025-05-27 12:51:08,260 - INFO - Database schema initialized successfully
2025-05-27 12:51:10,298 - INFO - DB Operation: {"timestamp": "2025-05-27 12:51:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:51:10,299 - INFO - DB Operation: {"timestamp": "2025-05-27 12:51:10", "operation": "CONNECT", "details": {"db_path": "data\\stats.db"}}
2025-05-27 12:51:10,299 - INFO - Connection pool initialized with 2 connections to data\stats.db
2025-05-27 13:28:19,901 - ERROR - Error initializing database: Index name conflict: `date` is the name of the primary key in:
r.table('daily_stats').index_create('date')
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
