2025-05-27 12:14:52.512 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.515 - Using OptimizedStats<PERSON>oader as secondary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.534 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.535 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-27 12:14:52.537 - Initializing StatsDataProvider
2025-05-27 12:14:52.550 - Loaded 8 items from cache
2025-05-27 12:14:52.553 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-27 12:14:52.556 - Starting background data loading thread
2025-05-27 12:14:52.564 - Background data loading started
2025-05-27 12:14:52.564 - StatsDataProvider initialization completed
2025-05-27 12:14:52.568 - Loading summary statistics
2025-05-27 12:14:52.570 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 12:14:52.570 - Created singleton instance of StatsDataProvider on module import
2025-05-27 12:14:52.571 - Data from GameStatsIntegration: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.571 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-27 12:14:52.579 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 12:14:52.586 - get_stats_provider called, returning provider with initialized=True
2025-05-27 12:14:52.587 - Successfully loaded summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:52.592 - Successfully loaded weekly stats: 7 days
2025-05-27 12:14:52.602 - Saved 10 items to cache
2025-05-27 12:14:52.603 - Background data loading completed
2025-05-27 13:37:03.489 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.492 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.526 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.529 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-27 13:37:03.529 - Initializing StatsDataProvider
2025-05-27 13:37:03.536 - Loaded 8 items from cache
2025-05-27 13:37:03.539 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-27 13:37:03.545 - Starting background data loading thread
2025-05-27 13:37:03.555 - Background data loading started
2025-05-27 13:37:03.555 - StatsDataProvider initialization completed
2025-05-27 13:37:03.556 - Loading summary statistics
2025-05-27 13:37:03.559 - Created singleton instance of StatsDataProvider on module import
2025-05-27 13:37:03.560 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:37:03.561 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-27 13:37:03.562 - Data from GameStatsIntegration: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.563 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:37:03.572 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:37:03.577 - Successfully loaded summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:37:03.583 - Successfully loaded weekly stats: 7 days
2025-05-27 13:37:03.586 - Saved 10 items to cache
2025-05-27 13:37:03.590 - Background data loading completed
2025-05-27 13:38:11.742 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.769 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.776 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.778 - Forcing refresh of all stats data
2025-05-27 13:38:11.779 - Attempting to force refresh via GameStatsIntegration
2025-05-27 13:38:11.798 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:38:11.799 - Force refresh via GameStatsIntegration successful
2025-05-27 13:38:11.800 - Clearing cached data
2025-05-27 13:38:11.801 - Posted refresh_stats event to trigger UI update
2025-05-27 13:38:11.802 - Starting background data reload
2025-05-27 13:38:11.810 - Starting background data loading thread
2025-05-27 13:38:11.812 - Background data loading started
2025-05-27 13:38:11.833 - Loading summary statistics
2025-05-27 13:38:11.866 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:38:11.917 - Data from GameStatsIntegration: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-27 13:38:11.922 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:38:11.926 - Successfully loaded summary stats: {'total_earnings': 40.0, 'daily_earnings': 40.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-27 13:38:11.927 - Successfully loaded game history: 1 entries
2025-05-27 13:38:11.928 - Successfully loaded weekly stats: 7 days
2025-05-27 13:38:11.930 - Saved 3 items to cache
2025-05-27 13:38:11.931 - Background data loading completed
2025-05-27 13:41:18.513 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.155 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.180 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.187 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.189 - Forcing refresh of all stats data
2025-05-27 13:42:18.190 - Attempting to force refresh via GameStatsIntegration
2025-05-27 13:42:18.210 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:42:18.219 - Force refresh via GameStatsIntegration successful
2025-05-27 13:42:18.220 - Clearing cached data
2025-05-27 13:42:18.221 - Posted refresh_stats event to trigger UI update
2025-05-27 13:42:18.222 - Starting background data reload
2025-05-27 13:42:18.223 - Starting background data loading thread
2025-05-27 13:42:18.223 - Background data loading started
2025-05-27 13:42:18.229 - Loading summary statistics
2025-05-27 13:42:18.277 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:42:18.288 - Data from GameStatsIntegration: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:42:18.310 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:42:18.313 - Successfully loaded summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:42:18.315 - Successfully loaded game history: 2 entries
2025-05-27 13:42:18.328 - Successfully loaded weekly stats: 7 days
2025-05-27 13:42:18.330 - Saved 3 items to cache
2025-05-27 13:42:18.333 - Background data loading completed
2025-05-27 13:55:04.784 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:04.786 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.439 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.606 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-27 13:55:05.615 - Initializing StatsDataProvider
2025-05-27 13:55:05.618 - Loaded 8 items from cache
2025-05-27 13:55:05.619 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-27 13:55:05.620 - Starting background data loading thread
2025-05-27 13:55:05.624 - Background data loading started
2025-05-27 13:55:05.624 - StatsDataProvider initialization completed
2025-05-27 13:55:05.627 - Loading summary statistics
2025-05-27 13:55:05.627 - Created singleton instance of StatsDataProvider on module import
2025-05-27 13:55:05.628 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:55:05.628 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-27 13:55:05.630 - Data from GameStatsIntegration: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.631 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:05.635 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:55:05.638 - Successfully loaded summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.643 - Successfully loaded game history: 2 entries
2025-05-27 13:55:05.647 - Successfully loaded weekly stats: 7 days
2025-05-27 13:55:05.650 - Saved 10 items to cache
2025-05-27 13:55:05.650 - Background data loading completed
2025-05-27 13:55:06.062 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.087 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.099 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.103 - Forcing refresh of all stats data
2025-05-27 13:55:06.104 - Attempting to force refresh via GameStatsIntegration
2025-05-27 13:55:06.122 - get_stats_provider called, returning provider with initialized=True
2025-05-27 13:55:06.131 - Force refresh via GameStatsIntegration successful
2025-05-27 13:55:06.136 - Clearing cached data
2025-05-27 13:55:06.137 - Starting background data reload
2025-05-27 13:55:06.138 - Starting background data loading thread
2025-05-27 13:55:06.139 - Background data loading started
2025-05-27 13:55:06.234 - Loading summary statistics
2025-05-27 13:55:06.235 - Attempting to load summary stats from GameStatsIntegration
2025-05-27 13:55:06.235 - Data from GameStatsIntegration: {'total_earnings': 220.0, 'daily_earnings': 220.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-27 13:55:06.235 - Successfully loaded summary stats from GameStatsIntegration
2025-05-27 13:55:06.236 - Successfully loaded summary stats: {'total_earnings': 220.0, 'daily_earnings': 220.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-27 13:55:06.237 - Successfully loaded game history: 3 entries
2025-05-27 13:55:06.238 - Successfully loaded weekly stats: 7 days
2025-05-27 13:55:06.240 - Saved 3 items to cache
2025-05-27 13:55:06.242 - Background data loading completed
2025-05-28 07:05:28.438 - Using GameStatsIntegration as primary data source. Test data: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.464 - Using OptimizedStatsLoader as secondary data source. Test data: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.487 - Using ThreadSafeDB as tertiary data source. Test data: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.489 - Data source availability: GameStatsIntegration=True, OptimizedLoader=True, ThreadSafeDB=True
2025-05-28 07:05:28.490 - Initializing StatsDataProvider
2025-05-28 07:05:28.534 - Loaded 8 items from cache
2025-05-28 07:05:28.535 - Cache contains keys: ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary']
2025-05-28 07:05:28.535 - Starting background data loading thread
2025-05-28 07:05:28.536 - StatsDataProvider initialization completed
2025-05-28 07:05:28.537 - Loading summary statistics
2025-05-28 07:05:28.538 - Created singleton instance of StatsDataProvider on module import
2025-05-28 07:05:28.539 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 07:05:28.539 - Diagnostic info: {'initialized': True, 'data_loading': True, 'cache_size': 8, 'cache_keys': ['weekly_stats', 'total_earnings', 'daily_earnings', 'daily_games', 'wallet_balance', 'total_games', 'game_history_page_0', 'wallet_summary'], 'last_error': None, 'error_time': None, 'successful_loads': {'summary_stats': 0, 'game_history': 0, 'weekly_stats': 0}, 'data_sources': {'STATS_INTEGRATION_AVAILABLE': True, 'OPTIMIZED_LOADER_AVAILABLE': True, 'THREAD_SAFE_DB_AVAILABLE': True}}
2025-05-28 07:05:28.540 - Data from GameStatsIntegration: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.540 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:05:28.540 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 07:05:28.542 - Successfully loaded summary stats: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:05:28.544 - Successfully loaded game history: 10 entries
2025-05-28 07:05:28.545 - Successfully loaded weekly stats: 7 days
2025-05-28 07:05:28.548 - Saved 10 items to cache
2025-05-28 07:05:28.549 - Background data loading completed
2025-05-28 07:07:29.896 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.929 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.950 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.953 - Forcing refresh of all stats data
2025-05-28 07:07:29.955 - Attempting to force refresh via GameStatsIntegration
2025-05-28 07:07:29.969 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:07:29.977 - Force refresh via GameStatsIntegration successful
2025-05-28 07:07:29.980 - Clearing cached data
2025-05-28 07:07:29.981 - Posted refresh_stats event to trigger UI update
2025-05-28 07:07:29.983 - Starting background data reload
2025-05-28 07:07:29.983 - Starting background data loading thread
2025-05-28 07:07:29.984 - Background data loading started
2025-05-28 07:07:30.039 - Loading summary statistics
2025-05-28 07:07:30.063 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 07:07:30.063 - Data from GameStatsIntegration: {'total_earnings': 310.0, 'daily_earnings': 80.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-28 07:07:30.108 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 07:07:30.108 - Successfully loaded summary stats: {'total_earnings': 310.0, 'daily_earnings': 80.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-28 07:07:30.108 - Successfully loaded game history: 10 entries
2025-05-28 07:07:30.109 - Successfully loaded weekly stats: 7 days
2025-05-28 07:07:30.110 - Saved 3 items to cache
2025-05-28 07:07:30.110 - Background data loading completed
2025-05-28 07:15:38.072 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:23:13.526 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:31:19.320 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:37:37.337 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:47:29.458 - get_stats_provider called, returning provider with initialized=True
2025-05-28 07:56:22.233 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:02:27.673 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:08:29.547 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:19:53.445 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:25:51.682 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:36:45.378 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.265 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.282 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.289 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.291 - Forcing refresh of all stats data
2025-05-28 08:50:01.292 - Attempting to force refresh via GameStatsIntegration
2025-05-28 08:50:01.306 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:50:01.307 - Force refresh via GameStatsIntegration successful
2025-05-28 08:50:01.308 - Clearing cached data
2025-05-28 08:50:01.309 - Posted refresh_stats event to trigger UI update
2025-05-28 08:50:01.310 - Starting background data reload
2025-05-28 08:50:01.312 - Starting background data loading thread
2025-05-28 08:50:01.313 - Background data loading started
2025-05-28 08:50:01.327 - Loading summary statistics
2025-05-28 08:50:01.386 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 08:50:01.389 - Data from GameStatsIntegration: {'total_earnings': 334.0, 'daily_earnings': 104.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-28 08:50:01.390 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 08:50:01.390 - Successfully loaded summary stats: {'total_earnings': 334.0, 'daily_earnings': 104.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-28 08:50:01.391 - Successfully loaded game history: 10 entries
2025-05-28 08:50:01.392 - Successfully loaded weekly stats: 7 days
2025-05-28 08:50:01.394 - Saved 3 items to cache
2025-05-28 08:50:01.394 - Background data loading completed
2025-05-28 08:50:11.757 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.523 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.640 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.670 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.680 - Forcing refresh of all stats data
2025-05-28 08:56:15.686 - Attempting to force refresh via GameStatsIntegration
2025-05-28 08:56:15.784 - get_stats_provider called, returning provider with initialized=True
2025-05-28 08:56:15.799 - Force refresh via GameStatsIntegration successful
2025-05-28 08:56:15.802 - Clearing cached data
2025-05-28 08:56:15.804 - Posted refresh_stats event to trigger UI update
2025-05-28 08:56:15.812 - Starting background data reload
2025-05-28 08:56:15.835 - Starting background data loading thread
2025-05-28 08:56:15.840 - Background data loading started
2025-05-28 08:56:15.858 - Loading summary statistics
2025-05-28 08:56:15.916 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 08:56:15.943 - Data from GameStatsIntegration: {'total_earnings': 358.0, 'daily_earnings': 128.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-28 08:56:15.954 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 08:56:15.989 - Successfully loaded summary stats: {'total_earnings': 358.0, 'daily_earnings': 128.0, 'daily_games': 3, 'wallet_balance': 0}
2025-05-28 08:56:15.992 - Successfully loaded game history: 10 entries
2025-05-28 08:56:15.993 - Successfully loaded weekly stats: 7 days
2025-05-28 08:56:15.998 - Saved 3 items to cache
2025-05-28 08:56:16.007 - Background data loading completed
2025-05-28 08:56:26.521 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.970 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.987 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.990 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:17.991 - Forcing refresh of all stats data
2025-05-28 09:29:17.992 - Attempting to force refresh via GameStatsIntegration
2025-05-28 09:29:18.008 - get_stats_provider called, returning provider with initialized=True
2025-05-28 09:29:18.010 - Force refresh via GameStatsIntegration successful
2025-05-28 09:29:18.011 - Clearing cached data
2025-05-28 09:29:18.012 - Posted refresh_stats event to trigger UI update
2025-05-28 09:29:18.012 - Starting background data reload
2025-05-28 09:29:18.013 - Starting background data loading thread
2025-05-28 09:29:18.017 - Background data loading started
2025-05-28 09:29:18.031 - Loading summary statistics
2025-05-28 09:29:18.073 - Attempting to load summary stats from GameStatsIntegration
2025-05-28 09:29:18.115 - Data from GameStatsIntegration: {'total_earnings': 382.0, 'daily_earnings': 152.0, 'daily_games': 4, 'wallet_balance': 0}
2025-05-28 09:29:18.116 - Successfully loaded summary stats from GameStatsIntegration
2025-05-28 09:29:18.116 - Successfully loaded summary stats: {'total_earnings': 382.0, 'daily_earnings': 152.0, 'daily_games': 4, 'wallet_balance': 0}
2025-05-28 09:29:18.117 - Successfully loaded game history: 10 entries
2025-05-28 09:29:18.118 - Successfully loaded weekly stats: 7 days
2025-05-28 09:29:18.119 - Saved 3 items to cache
2025-05-28 09:29:18.119 - Background data loading completed
