2025-05-27 11:35:25.350 - ensure_database_exists called from thread 14364
2025-05-27 11:35:25.353 - Creating new thread-specific database connection to data\stats.db for thread 14364
2025-05-27 11:35:25.354 - New database connection created successfully for thread 14364
2025-05-27 11:35:25.394 - Stats database initialized successfully
2025-05-27 11:35:25.395 - ensure_database_exists called from thread 14364
2025-05-27 11:35:25.396 - Using existing connection for thread 14364
2025-05-27 11:35:25.399 - Stats database initialized successfully
2025-05-27 11:35:25.401 - ensure_database_exists called from thread 14364
2025-05-27 11:35:25.404 - Using existing connection for thread 14364
2025-05-27 11:35:25.404 - Stats database initialized successfully
2025-05-27 11:55:24.219 - ensure_database_exists called from thread 12412
2025-05-27 11:55:24.221 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-05-27 11:55:24.224 - New database connection created successfully for thread 12412
2025-05-27 11:55:24.226 - Stats database initialized successfully
2025-05-27 11:55:24.227 - ensure_database_exists called from thread 12412
2025-05-27 11:55:24.228 - Using existing connection for thread 12412
2025-05-27 11:55:24.229 - Stats database initialized successfully
2025-05-27 11:55:24.229 - ensure_database_exists called from thread 12412
2025-05-27 11:55:24.231 - Using existing connection for thread 12412
2025-05-27 11:55:24.232 - Stats database initialized successfully
2025-05-27 11:57:27.341 - ensure_database_exists called from thread 13388
2025-05-27 11:57:27.342 - Creating new thread-specific database connection to data\stats.db for thread 13388
2025-05-27 11:57:27.343 - New database connection created successfully for thread 13388
2025-05-27 11:57:27.345 - Stats database initialized successfully
2025-05-27 11:57:27.346 - ensure_database_exists called from thread 13388
2025-05-27 11:57:27.347 - Using existing connection for thread 13388
2025-05-27 11:57:27.349 - Stats database initialized successfully
2025-05-27 11:57:27.350 - ensure_database_exists called from thread 13388
2025-05-27 11:57:27.351 - Using existing connection for thread 13388
2025-05-27 11:57:27.351 - Stats database initialized successfully
2025-05-27 12:00:19.550 - ensure_database_exists called from thread 15092
2025-05-27 12:00:19.552 - Creating new thread-specific database connection to data\stats.db for thread 15092
2025-05-27 12:00:19.553 - New database connection created successfully for thread 15092
2025-05-27 12:00:19.556 - Stats database initialized successfully
2025-05-27 12:00:19.557 - ensure_database_exists called from thread 15092
2025-05-27 12:00:19.558 - Using existing connection for thread 15092
2025-05-27 12:00:19.558 - Stats database initialized successfully
2025-05-27 12:00:19.560 - ensure_database_exists called from thread 15092
2025-05-27 12:00:19.560 - Using existing connection for thread 15092
2025-05-27 12:00:19.561 - Stats database initialized successfully
2025-05-27 12:01:39.337 - ensure_database_exists called from thread 8464
2025-05-27 12:01:39.338 - Creating new thread-specific database connection to data\stats.db for thread 8464
2025-05-27 12:01:39.339 - New database connection created successfully for thread 8464
2025-05-27 12:01:39.343 - Stats database initialized successfully
2025-05-27 12:01:39.343 - ensure_database_exists called from thread 8464
2025-05-27 12:01:39.345 - Using existing connection for thread 8464
2025-05-27 12:01:39.346 - Stats database initialized successfully
2025-05-27 12:01:39.347 - ensure_database_exists called from thread 8464
2025-05-27 12:01:39.348 - Using existing connection for thread 8464
2025-05-27 12:01:39.349 - Stats database initialized successfully
2025-05-27 12:03:22.044 - ensure_database_exists called from thread 7980
2025-05-27 12:03:22.046 - Creating new thread-specific database connection to data\stats.db for thread 7980
2025-05-27 12:03:22.047 - New database connection created successfully for thread 7980
2025-05-27 12:03:22.050 - Stats database initialized successfully
2025-05-27 12:03:22.051 - ensure_database_exists called from thread 7980
2025-05-27 12:03:22.052 - Using existing connection for thread 7980
2025-05-27 12:03:22.053 - Stats database initialized successfully
2025-05-27 12:03:22.055 - ensure_database_exists called from thread 7980
2025-05-27 12:03:22.056 - Using existing connection for thread 7980
2025-05-27 12:03:22.057 - Stats database initialized successfully
2025-05-27 12:06:49.953 - ensure_database_exists called from thread 15212
2025-05-27 12:06:49.953 - Creating new thread-specific database connection to data\stats.db for thread 15212
2025-05-27 12:06:49.953 - New database connection created successfully for thread 15212
2025-05-27 12:06:49.953 - Stats database initialized successfully
2025-05-27 12:06:49.961 - ensure_database_exists called from thread 15212
2025-05-27 12:06:49.968 - Using existing connection for thread 15212
2025-05-27 12:06:49.969 - Stats database initialized successfully
2025-05-27 12:07:27.578 - ensure_database_exists called from thread 15280
2025-05-27 12:07:27.578 - Creating new thread-specific database connection to data\stats.db for thread 15280
2025-05-27 12:07:27.586 - New database connection created successfully for thread 15280
2025-05-27 12:07:27.586 - Stats database initialized successfully
2025-05-27 12:07:27.586 - ensure_database_exists called from thread 15280
2025-05-27 12:07:27.586 - Using existing connection for thread 15280
2025-05-27 12:07:27.586 - Stats database initialized successfully
2025-05-27 12:09:41.546 - ensure_database_exists called from thread 9992
2025-05-27 12:09:41.552 - Creating new thread-specific database connection to data\stats.db for thread 9992
2025-05-27 12:09:41.553 - New database connection created successfully for thread 9992
2025-05-27 12:09:41.555 - Stats database initialized successfully
2025-05-27 12:09:41.556 - ensure_database_exists called from thread 9992
2025-05-27 12:09:41.557 - Using existing connection for thread 9992
2025-05-27 12:09:41.558 - Stats database initialized successfully
2025-05-27 12:09:41.559 - ensure_database_exists called from thread 9992
2025-05-27 12:09:41.560 - Using existing connection for thread 9992
2025-05-27 12:09:41.562 - Stats database initialized successfully
2025-05-27 12:12:52.972 - ensure_database_exists called from thread 12412
2025-05-27 12:12:52.982 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-05-27 12:12:52.988 - New database connection created successfully for thread 12412
2025-05-27 12:12:52.988 - Stats database initialized successfully
2025-05-27 12:12:52.988 - ensure_database_exists called from thread 12412
2025-05-27 12:12:52.988 - Using existing connection for thread 12412
2025-05-27 12:12:52.988 - Stats database initialized successfully
2025-05-27 12:13:59.226 - ensure_database_exists called from thread 13140
2025-05-27 12:13:59.228 - Creating new thread-specific database connection to data\stats.db for thread 13140
2025-05-27 12:13:59.230 - New database connection created successfully for thread 13140
2025-05-27 12:13:59.232 - Stats database initialized successfully
2025-05-27 12:13:59.235 - ensure_database_exists called from thread 13140
2025-05-27 12:13:59.237 - Using existing connection for thread 13140
2025-05-27 12:13:59.238 - Stats database initialized successfully
2025-05-27 12:13:59.239 - ensure_database_exists called from thread 13140
2025-05-27 12:13:59.240 - Using existing connection for thread 13140
2025-05-27 12:13:59.241 - Stats database initialized successfully
2025-05-27 12:14:52.431 - Using existing connection for thread 13140
2025-05-27 12:14:52.450 - Creating new thread-specific database connection to data\stats.db for thread 14656
2025-05-27 12:14:52.451 - New database connection created successfully for thread 14656
2025-05-27 12:14:52.472 - Database connection closed for thread 14656
2025-05-27 12:14:52.481 - Creating new thread-specific database connection to data\stats.db for thread 14656
2025-05-27 12:14:52.484 - New database connection created successfully for thread 14656
2025-05-27 12:14:52.517 - get_summary_stats called from thread 14656
2025-05-27 12:14:52.517 - Using existing connection for thread 14656
2025-05-27 12:14:52.520 - Total earnings from database: 0
2025-05-27 12:14:52.522 - Daily earnings from database: 0
2025-05-27 12:14:52.523 - Daily games from database: 0
2025-05-27 12:14:52.527 - Wallet balance from database: 0
2025-05-27 12:14:52.531 - Total games played from database: 0
2025-05-27 12:14:52.532 - Total winners from database: 0
2025-05-27 12:14:52.533 - Returning summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:59.149 - ensure_database_exists called from thread 13140
2025-05-27 12:14:59.149 - Using existing connection for thread 13140
2025-05-27 12:14:59.151 - Stats database initialized successfully
2025-05-27 12:18:30.234 - ensure_database_exists called from thread 2608
2025-05-27 12:18:30.235 - Creating new thread-specific database connection to data\stats.db for thread 2608
2025-05-27 12:18:30.237 - New database connection created successfully for thread 2608
2025-05-27 12:18:30.241 - Stats database initialized successfully
2025-05-27 12:18:30.242 - ensure_database_exists called from thread 2608
2025-05-27 12:18:30.244 - Using existing connection for thread 2608
2025-05-27 12:18:30.245 - Stats database initialized successfully
2025-05-27 12:18:30.246 - ensure_database_exists called from thread 2608
2025-05-27 12:18:30.247 - Using existing connection for thread 2608
2025-05-27 12:18:30.248 - Stats database initialized successfully
2025-05-27 12:22:01.819 - ensure_database_exists called from thread 3268
2025-05-27 12:22:01.819 - Creating new thread-specific database connection to data\stats.db for thread 3268
2025-05-27 12:22:01.819 - New database connection created successfully for thread 3268
2025-05-27 12:22:01.827 - Stats database initialized successfully
2025-05-27 12:22:01.827 - ensure_database_exists called from thread 3268
2025-05-27 12:22:01.835 - Using existing connection for thread 3268
2025-05-27 12:22:01.835 - Stats database initialized successfully
2025-05-27 12:22:32.006 - ensure_database_exists called from thread 8620
2025-05-27 12:22:32.014 - Creating new thread-specific database connection to data\stats.db for thread 8620
2025-05-27 12:22:32.014 - New database connection created successfully for thread 8620
2025-05-27 12:22:32.014 - Stats database initialized successfully
2025-05-27 12:22:32.014 - ensure_database_exists called from thread 8620
2025-05-27 12:22:32.022 - Using existing connection for thread 8620
2025-05-27 12:22:32.022 - Stats database initialized successfully
2025-05-27 12:24:02.157 - ensure_database_exists called from thread 8468
2025-05-27 12:24:02.158 - Creating new thread-specific database connection to data\stats.db for thread 8468
2025-05-27 12:24:02.160 - New database connection created successfully for thread 8468
2025-05-27 12:24:02.162 - Stats database initialized successfully
2025-05-27 12:24:02.163 - ensure_database_exists called from thread 8468
2025-05-27 12:24:02.164 - Using existing connection for thread 8468
2025-05-27 12:24:02.165 - Stats database initialized successfully
2025-05-27 12:24:02.167 - ensure_database_exists called from thread 8468
2025-05-27 12:24:02.168 - Using existing connection for thread 8468
2025-05-27 12:24:02.169 - Stats database initialized successfully
2025-05-27 12:26:07.987 - ensure_database_exists called from thread 4456
2025-05-27 12:26:07.995 - Creating new thread-specific database connection to data\stats.db for thread 4456
2025-05-27 12:26:07.995 - New database connection created successfully for thread 4456
2025-05-27 12:26:08.008 - Stats database initialized successfully
2025-05-27 12:26:08.011 - ensure_database_exists called from thread 4456
2025-05-27 12:26:08.011 - Using existing connection for thread 4456
2025-05-27 12:26:08.019 - Stats database initialized successfully
2025-05-27 12:27:11.806 - ensure_database_exists called from thread 9984
2025-05-27 12:27:11.814 - Creating new thread-specific database connection to data\stats.db for thread 9984
2025-05-27 12:27:11.814 - New database connection created successfully for thread 9984
2025-05-27 12:27:11.822 - Stats database initialized successfully
2025-05-27 12:27:11.830 - ensure_database_exists called from thread 9984
2025-05-27 12:27:11.830 - Using existing connection for thread 9984
2025-05-27 12:27:11.830 - Stats database initialized successfully
2025-05-27 12:27:43.125 - ensure_database_exists called from thread 10712
2025-05-27 12:27:43.128 - Creating new thread-specific database connection to data\stats.db for thread 10712
2025-05-27 12:27:43.131 - New database connection created successfully for thread 10712
2025-05-27 12:27:43.137 - Stats database initialized successfully
2025-05-27 12:27:43.139 - ensure_database_exists called from thread 10712
2025-05-27 12:27:43.141 - Using existing connection for thread 10712
2025-05-27 12:27:43.143 - Stats database initialized successfully
2025-05-27 12:28:20.097 - ensure_database_exists called from thread 7320
2025-05-27 12:28:20.105 - Creating new thread-specific database connection to data\stats.db for thread 7320
2025-05-27 12:28:20.105 - New database connection created successfully for thread 7320
2025-05-27 12:28:20.108 - Stats database initialized successfully
2025-05-27 12:28:20.112 - ensure_database_exists called from thread 7320
2025-05-27 12:28:20.116 - Using existing connection for thread 7320
2025-05-27 12:28:20.120 - Stats database initialized successfully
2025-05-27 12:30:02.765 - ensure_database_exists called from thread 12472
2025-05-27 12:30:02.766 - Creating new thread-specific database connection to data\stats.db for thread 12472
2025-05-27 12:30:02.767 - New database connection created successfully for thread 12472
2025-05-27 12:30:02.769 - Stats database initialized successfully
2025-05-27 12:30:02.770 - ensure_database_exists called from thread 12472
2025-05-27 12:30:02.770 - Using existing connection for thread 12472
2025-05-27 12:30:02.771 - Stats database initialized successfully
2025-05-27 12:30:02.772 - ensure_database_exists called from thread 12472
2025-05-27 12:30:02.773 - Using existing connection for thread 12472
2025-05-27 12:30:02.774 - Stats database initialized successfully
2025-05-27 12:32:46.493 - ensure_database_exists called from thread 13936
2025-05-27 12:32:46.495 - Creating new thread-specific database connection to data\stats.db for thread 13936
2025-05-27 12:32:46.495 - New database connection created successfully for thread 13936
2025-05-27 12:32:46.499 - Stats database initialized successfully
2025-05-27 12:32:46.500 - ensure_database_exists called from thread 13936
2025-05-27 12:32:46.501 - Using existing connection for thread 13936
2025-05-27 12:32:46.502 - Stats database initialized successfully
2025-05-27 12:32:46.503 - ensure_database_exists called from thread 13936
2025-05-27 12:32:46.503 - Using existing connection for thread 13936
2025-05-27 12:32:46.504 - Stats database initialized successfully
2025-05-27 12:35:25.872 - ensure_database_exists called from thread 11960
2025-05-27 12:35:25.873 - Creating new thread-specific database connection to data\stats.db for thread 11960
2025-05-27 12:35:25.875 - New database connection created successfully for thread 11960
2025-05-27 12:35:25.878 - Stats database initialized successfully
2025-05-27 12:35:25.879 - ensure_database_exists called from thread 11960
2025-05-27 12:35:25.880 - Using existing connection for thread 11960
2025-05-27 12:35:25.880 - Stats database initialized successfully
2025-05-27 12:35:25.882 - ensure_database_exists called from thread 11960
2025-05-27 12:35:25.883 - Using existing connection for thread 11960
2025-05-27 12:35:25.883 - Stats database initialized successfully
2025-05-27 12:44:45.107 - ensure_database_exists called from thread 8896
2025-05-27 12:44:45.109 - Creating new thread-specific database connection to data\stats.db for thread 8896
2025-05-27 12:44:45.110 - New database connection created successfully for thread 8896
2025-05-27 12:44:45.319 - Stats database initialized successfully
2025-05-27 12:44:45.323 - ensure_database_exists called from thread 8896
2025-05-27 12:44:45.324 - Using existing connection for thread 8896
2025-05-27 12:44:45.325 - Stats database initialized successfully
2025-05-27 12:51:26.984 - ensure_database_exists called from thread 6480
2025-05-27 12:51:26.990 - Creating new thread-specific database connection to data\stats.db for thread 6480
2025-05-27 12:51:26.991 - New database connection created successfully for thread 6480
2025-05-27 12:51:26.998 - Stats database initialized successfully
2025-05-27 12:51:26.999 - ensure_database_exists called from thread 6480
2025-05-27 12:51:27.000 - Using existing connection for thread 6480
2025-05-27 12:51:27.001 - Stats database initialized successfully
2025-05-27 12:52:48.548 - ensure_database_exists called from thread 12160
2025-05-27 12:52:48.550 - Creating new thread-specific database connection to data\stats.db for thread 12160
2025-05-27 12:52:48.552 - New database connection created successfully for thread 12160
2025-05-27 12:52:48.554 - Stats database initialized successfully
2025-05-27 12:52:48.555 - ensure_database_exists called from thread 12160
2025-05-27 12:52:48.556 - Using existing connection for thread 12160
2025-05-27 12:52:48.557 - Stats database initialized successfully
2025-05-27 12:52:48.558 - ensure_database_exists called from thread 12160
2025-05-27 12:52:48.558 - Using existing connection for thread 12160
2025-05-27 12:52:48.559 - Stats database initialized successfully
2025-05-27 12:54:50.572 - ensure_database_exists called from thread 10232
2025-05-27 12:54:50.576 - Creating new thread-specific database connection to data\stats.db for thread 10232
2025-05-27 12:54:50.578 - New database connection created successfully for thread 10232
2025-05-27 12:54:50.580 - Stats database initialized successfully
2025-05-27 12:54:50.581 - ensure_database_exists called from thread 10232
2025-05-27 12:54:50.581 - Using existing connection for thread 10232
2025-05-27 12:54:50.582 - Stats database initialized successfully
2025-05-27 12:57:59.435 - ensure_database_exists called from thread 10008
2025-05-27 12:57:59.436 - Creating new thread-specific database connection to data\stats.db for thread 10008
2025-05-27 12:57:59.438 - New database connection created successfully for thread 10008
2025-05-27 12:57:59.441 - Stats database initialized successfully
2025-05-27 12:57:59.442 - ensure_database_exists called from thread 10008
2025-05-27 12:57:59.444 - Using existing connection for thread 10008
2025-05-27 12:57:59.445 - Stats database initialized successfully
2025-05-27 12:58:14.467 - ensure_database_exists called from thread 14656
2025-05-27 12:58:14.470 - Creating new thread-specific database connection to data\stats.db for thread 14656
2025-05-27 12:58:14.473 - New database connection created successfully for thread 14656
2025-05-27 12:58:14.475 - Stats database initialized successfully
2025-05-27 12:58:14.476 - ensure_database_exists called from thread 14656
2025-05-27 12:58:14.477 - Using existing connection for thread 14656
2025-05-27 12:58:14.478 - Stats database initialized successfully
2025-05-27 12:58:14.480 - ensure_database_exists called from thread 14656
2025-05-27 12:58:14.480 - Using existing connection for thread 14656
2025-05-27 12:58:14.481 - Stats database initialized successfully
2025-05-27 13:10:06.268 - ensure_database_exists called from thread 7968
2025-05-27 13:10:06.269 - Creating new thread-specific database connection to data\stats.db for thread 7968
2025-05-27 13:10:06.270 - New database connection created successfully for thread 7968
2025-05-27 13:10:06.274 - Stats database initialized successfully
2025-05-27 13:10:06.275 - ensure_database_exists called from thread 7968
2025-05-27 13:10:06.276 - Using existing connection for thread 7968
2025-05-27 13:10:06.278 - Stats database initialized successfully
2025-05-27 13:14:27.587 - ensure_database_exists called from thread 2620
2025-05-27 13:14:27.589 - Creating new thread-specific database connection to data\stats.db for thread 2620
2025-05-27 13:14:27.591 - New database connection created successfully for thread 2620
2025-05-27 13:14:27.593 - Stats database initialized successfully
2025-05-27 13:14:27.594 - ensure_database_exists called from thread 2620
2025-05-27 13:14:27.595 - Using existing connection for thread 2620
2025-05-27 13:14:27.596 - Stats database initialized successfully
2025-05-27 13:14:59.765 - ensure_database_exists called from thread 12140
2025-05-27 13:14:59.767 - Creating new thread-specific database connection to data\stats.db for thread 12140
2025-05-27 13:14:59.768 - New database connection created successfully for thread 12140
2025-05-27 13:14:59.776 - Stats database initialized successfully
2025-05-27 13:14:59.777 - ensure_database_exists called from thread 12140
2025-05-27 13:14:59.778 - Using existing connection for thread 12140
2025-05-27 13:14:59.779 - Stats database initialized successfully
2025-05-27 13:21:59.164 - ensure_database_exists called from thread 8312
2025-05-27 13:21:59.165 - Creating new thread-specific database connection to data\stats.db for thread 8312
2025-05-27 13:21:59.167 - New database connection created successfully for thread 8312
2025-05-27 13:21:59.169 - Stats database initialized successfully
2025-05-27 13:21:59.169 - ensure_database_exists called from thread 8312
2025-05-27 13:21:59.170 - Using existing connection for thread 8312
2025-05-27 13:21:59.170 - Stats database initialized successfully
2025-05-27 13:21:59.171 - ensure_database_exists called from thread 8312
2025-05-27 13:21:59.171 - Using existing connection for thread 8312
2025-05-27 13:21:59.172 - Stats database initialized successfully
2025-05-27 13:30:49.707 - ensure_database_exists called from thread 12056
2025-05-27 13:30:49.708 - Creating new thread-specific database connection to data\stats.db for thread 12056
2025-05-27 13:30:49.709 - New database connection created successfully for thread 12056
2025-05-27 13:30:49.710 - Stats database initialized successfully
2025-05-27 13:30:49.711 - ensure_database_exists called from thread 12056
2025-05-27 13:30:49.712 - Using existing connection for thread 12056
2025-05-27 13:30:49.713 - Stats database initialized successfully
2025-05-27 13:30:49.714 - ensure_database_exists called from thread 12056
2025-05-27 13:30:49.714 - Using existing connection for thread 12056
2025-05-27 13:30:49.715 - Stats database initialized successfully
2025-05-27 13:33:12.033 - ensure_database_exists called from thread 2420
2025-05-27 13:33:12.034 - Creating new thread-specific database connection to data\stats.db for thread 2420
2025-05-27 13:33:12.035 - New database connection created successfully for thread 2420
2025-05-27 13:33:12.036 - Stats database initialized successfully
2025-05-27 13:33:12.037 - ensure_database_exists called from thread 2420
2025-05-27 13:33:12.037 - Using existing connection for thread 2420
2025-05-27 13:33:12.038 - Stats database initialized successfully
2025-05-27 13:33:12.038 - ensure_database_exists called from thread 2420
2025-05-27 13:33:12.039 - Using existing connection for thread 2420
2025-05-27 13:33:12.039 - Stats database initialized successfully
2025-05-27 13:36:10.471 - ensure_database_exists called from thread 11900
2025-05-27 13:36:10.473 - Creating new thread-specific database connection to data\stats.db for thread 11900
2025-05-27 13:36:10.475 - New database connection created successfully for thread 11900
2025-05-27 13:36:10.477 - Stats database initialized successfully
2025-05-27 13:36:10.478 - ensure_database_exists called from thread 11900
2025-05-27 13:36:10.479 - Using existing connection for thread 11900
2025-05-27 13:36:10.480 - Stats database initialized successfully
2025-05-27 13:36:10.481 - ensure_database_exists called from thread 11900
2025-05-27 13:36:10.482 - Using existing connection for thread 11900
2025-05-27 13:36:10.487 - Stats database initialized successfully
2025-05-27 13:37:03.279 - Using existing connection for thread 11900
2025-05-27 13:37:03.312 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:37:03.313 - New database connection created successfully for thread 12792
2025-05-27 13:37:03.452 - Database connection closed for thread 12792
2025-05-27 13:37:03.458 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:37:03.461 - New database connection created successfully for thread 12792
2025-05-27 13:37:03.468 - Database connection closed for thread 12792
2025-05-27 13:37:03.473 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:37:03.477 - New database connection created successfully for thread 12792
2025-05-27 13:37:03.493 - get_summary_stats called from thread 12792
2025-05-27 13:37:03.495 - Using existing connection for thread 12792
2025-05-27 13:37:03.498 - Total earnings from database: 0.0
2025-05-27 13:37:03.500 - Daily earnings from database: 0.0
2025-05-27 13:37:03.507 - Daily games from database: 0
2025-05-27 13:37:03.508 - Wallet balance from database: 0
2025-05-27 13:37:03.510 - Total games played from database: 0
2025-05-27 13:37:03.513 - Total winners from database: 0
2025-05-27 13:37:03.519 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:38:08.630 - Using existing connection for thread 12792
2025-05-27 13:38:08.661 - ensure_database_exists called from thread 11900
2025-05-27 13:38:08.662 - Using existing connection for thread 11900
2025-05-27 13:38:08.663 - Stats database initialized successfully
2025-05-27 13:38:11.660 - Database connection closed for thread 12792
2025-05-27 13:38:11.694 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.696 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.715 - Database connection closed for thread 12792
2025-05-27 13:38:11.717 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.718 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.729 - Database connection closed for thread 12792
2025-05-27 13:38:11.731 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.733 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.748 - Database connection closed for thread 12792
2025-05-27 13:38:11.749 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.751 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.761 - Database connection closed for thread 12792
2025-05-27 13:38:11.763 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.764 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.783 - Database connection closed for thread 12792
2025-05-27 13:38:11.783 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.784 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.792 - Database connection closed for thread 12792
2025-05-27 13:38:11.793 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.794 - New database connection created successfully for thread 12792
2025-05-27 13:41:18.383 - Using existing connection for thread 11900
2025-05-27 13:41:18.422 - Using existing connection for thread 12792
2025-05-27 13:41:18.480 - Database connection closed for thread 12792
2025-05-27 13:41:18.483 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:41:18.486 - New database connection created successfully for thread 12792
2025-05-27 13:41:18.497 - Database connection closed for thread 12792
2025-05-27 13:41:18.499 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:41:18.501 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.068 - Using existing connection for thread 12792
2025-05-27 13:42:18.133 - Database connection closed for thread 12792
2025-05-27 13:42:18.135 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.136 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.138 - Database connection closed for thread 12792
2025-05-27 13:42:18.139 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.140 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.146 - Database connection closed for thread 12792
2025-05-27 13:42:18.150 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.152 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.158 - Database connection closed for thread 12792
2025-05-27 13:42:18.162 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.163 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.171 - Database connection closed for thread 12792
2025-05-27 13:42:18.172 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.175 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.192 - Database connection closed for thread 12792
2025-05-27 13:42:18.193 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.195 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.202 - Database connection closed for thread 12792
2025-05-27 13:42:18.203 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.205 - New database connection created successfully for thread 12792
2025-05-27 13:55:03.086 - ensure_database_exists called from thread 14704
2025-05-27 13:55:03.088 - Creating new thread-specific database connection to data\stats.db for thread 14704
2025-05-27 13:55:03.091 - New database connection created successfully for thread 14704
2025-05-27 13:55:03.094 - Stats database initialized successfully
2025-05-27 13:55:03.095 - ensure_database_exists called from thread 14704
2025-05-27 13:55:03.096 - Using existing connection for thread 14704
2025-05-27 13:55:03.100 - Stats database initialized successfully
2025-05-27 13:55:03.210 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:03.211 - New database connection created successfully for thread 14308
2025-05-27 13:55:04.755 - Database connection closed for thread 14308
2025-05-27 13:55:04.756 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:04.758 - New database connection created successfully for thread 14308
2025-05-27 13:55:04.764 - Database connection closed for thread 14308
2025-05-27 13:55:04.765 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:04.766 - New database connection created successfully for thread 14308
2025-05-27 13:55:05.400 - get_summary_stats called from thread 14308
2025-05-27 13:55:05.403 - Using existing connection for thread 14308
2025-05-27 13:55:05.417 - Total earnings from database: 120.0
2025-05-27 13:55:05.418 - Daily earnings from database: 120.0
2025-05-27 13:55:05.420 - Daily games from database: 2
2025-05-27 13:55:05.422 - Wallet balance from database: 0
2025-05-27 13:55:05.423 - Total games played from database: 2
2025-05-27 13:55:05.429 - Total winners from database: 2
2025-05-27 13:55:05.438 - Returning summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.748 - Using existing connection for thread 14308
2025-05-27 13:55:06.034 - Database connection closed for thread 14308
2025-05-27 13:55:06.037 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.038 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.042 - Database connection closed for thread 14308
2025-05-27 13:55:06.043 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.044 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.047 - Database connection closed for thread 14308
2025-05-27 13:55:06.048 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.055 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.066 - Database connection closed for thread 14308
2025-05-27 13:55:06.067 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.070 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.081 - Database connection closed for thread 14308
2025-05-27 13:55:06.082 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.083 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.105 - Database connection closed for thread 14308
2025-05-27 13:55:06.105 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.106 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.115 - Database connection closed for thread 14308
2025-05-27 13:55:06.116 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.118 - New database connection created successfully for thread 14308
2025-05-27 13:56:09.174 - ensure_database_exists called from thread 13528
2025-05-27 13:56:09.178 - Creating new thread-specific database connection to data\stats.db for thread 13528
2025-05-27 13:56:09.183 - New database connection created successfully for thread 13528
2025-05-27 13:56:09.189 - Stats database initialized successfully
2025-05-27 13:56:09.194 - Using existing connection for thread 13528
2025-05-27 13:56:09.683 - Database connection closed for thread 13528
2025-05-27 13:57:15.206 - ensure_database_exists called from thread 16148
2025-05-27 13:57:15.222 - Creating new thread-specific database connection to data\stats.db for thread 16148
2025-05-27 13:57:16.193 - New database connection created successfully for thread 16148
2025-05-27 13:57:16.208 - Stats database initialized successfully
2025-05-27 13:57:16.220 - ensure_database_exists called from thread 16148
2025-05-27 13:57:16.224 - Using existing connection for thread 16148
2025-05-27 13:57:16.246 - Stats database initialized successfully
2025-05-27 13:57:16.282 - ensure_database_exists called from thread 16148
2025-05-27 13:57:16.299 - Using existing connection for thread 16148
2025-05-27 13:57:16.338 - Stats database initialized successfully
2025-05-28 06:47:01.928 - ensure_database_exists called from thread 12932
2025-05-28 06:47:02.036 - Creating new thread-specific database connection to data\stats.db for thread 12932
2025-05-28 06:47:02.037 - New database connection created successfully for thread 12932
2025-05-28 06:47:02.089 - Stats database initialized successfully
2025-05-28 06:47:02.090 - ensure_database_exists called from thread 12932
2025-05-28 06:47:02.090 - Using existing connection for thread 12932
2025-05-28 06:47:02.091 - Stats database initialized successfully
2025-05-28 06:47:02.092 - ensure_database_exists called from thread 12932
2025-05-28 06:47:02.092 - Using existing connection for thread 12932
2025-05-28 06:47:02.093 - Stats database initialized successfully
2025-05-28 07:05:28.153 - Recreating expired connection for thread 12932
2025-05-28 07:05:28.201 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:05:28.232 - New database connection created successfully for thread 11724
2025-05-28 07:05:28.332 - Database connection closed for thread 11724
2025-05-28 07:05:28.333 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:05:28.344 - New database connection created successfully for thread 11724
2025-05-28 07:05:28.372 - Database connection closed for thread 11724
2025-05-28 07:05:28.387 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:05:28.388 - New database connection created successfully for thread 11724
2025-05-28 07:05:28.465 - get_summary_stats called from thread 11724
2025-05-28 07:05:28.466 - Using existing connection for thread 11724
2025-05-28 07:05:28.467 - Total earnings from database: 230.0
2025-05-28 07:05:28.468 - Daily earnings from database: 0.0
2025-05-28 07:05:28.471 - Daily games from database: 0
2025-05-28 07:05:28.476 - Wallet balance from database: 0
2025-05-28 07:05:28.480 - Total games played from database: 4
2025-05-28 07:05:28.483 - Total winners from database: 4
2025-05-28 07:05:28.484 - Returning summary stats: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:07:26.523 - Using existing connection for thread 11724
2025-05-28 07:07:26.556 - ensure_database_exists called from thread 12932
2025-05-28 07:07:26.557 - Using existing connection for thread 12932
2025-05-28 07:07:26.558 - Stats database initialized successfully
2025-05-28 07:07:29.413 - Database connection closed for thread 11724
2025-05-28 07:07:29.838 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.840 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.842 - Database connection closed for thread 11724
2025-05-28 07:07:29.843 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.843 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.847 - Database connection closed for thread 11724
2025-05-28 07:07:29.849 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.857 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.910 - Database connection closed for thread 11724
2025-05-28 07:07:29.912 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.913 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.916 - Database connection closed for thread 11724
2025-05-28 07:07:29.918 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.921 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.957 - Database connection closed for thread 11724
2025-05-28 07:07:29.958 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.958 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.963 - Database connection closed for thread 11724
2025-05-28 07:07:29.963 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.964 - New database connection created successfully for thread 11724
2025-05-28 07:15:37.953 - Recreating expired connection for thread 12932
2025-05-28 07:15:38.050 - Database connection closed for thread 11724
2025-05-28 07:15:38.051 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:15:38.051 - New database connection created successfully for thread 11724
2025-05-28 07:15:38.053 - Database connection closed for thread 11724
2025-05-28 07:15:38.054 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:15:38.056 - New database connection created successfully for thread 11724
2025-05-28 07:23:13.460 - Recreating expired connection for thread 12932
2025-05-28 07:23:13.509 - Database connection closed for thread 11724
2025-05-28 07:23:13.513 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:23:13.515 - New database connection created successfully for thread 11724
2025-05-28 07:23:13.519 - Database connection closed for thread 11724
2025-05-28 07:23:13.520 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:23:13.520 - New database connection created successfully for thread 11724
2025-05-28 07:31:19.251 - Recreating expired connection for thread 12932
2025-05-28 07:31:19.307 - Database connection closed for thread 11724
2025-05-28 07:31:19.308 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:31:19.308 - New database connection created successfully for thread 11724
2025-05-28 07:31:19.310 - Database connection closed for thread 11724
2025-05-28 07:31:19.311 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:31:19.312 - New database connection created successfully for thread 11724
2025-05-28 07:37:37.291 - Recreating expired connection for thread 12932
2025-05-28 07:37:37.320 - Database connection closed for thread 11724
2025-05-28 07:37:37.321 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:37:37.322 - New database connection created successfully for thread 11724
2025-05-28 07:37:37.325 - Database connection closed for thread 11724
2025-05-28 07:37:37.326 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:37:37.328 - New database connection created successfully for thread 11724
2025-05-28 07:47:29.380 - Recreating expired connection for thread 12932
2025-05-28 07:47:29.423 - Database connection closed for thread 11724
2025-05-28 07:47:29.427 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:47:29.432 - New database connection created successfully for thread 11724
2025-05-28 07:47:29.440 - Database connection closed for thread 11724
2025-05-28 07:47:29.447 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:47:29.451 - New database connection created successfully for thread 11724
2025-05-28 07:56:22.199 - Recreating expired connection for thread 12932
2025-05-28 07:56:22.212 - Database connection closed for thread 11724
2025-05-28 07:56:22.213 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:56:22.214 - New database connection created successfully for thread 11724
2025-05-28 07:56:22.221 - Database connection closed for thread 11724
2025-05-28 07:56:22.222 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:56:22.224 - New database connection created successfully for thread 11724
2025-05-28 08:02:27.610 - Recreating expired connection for thread 12932
2025-05-28 08:02:27.661 - Database connection closed for thread 11724
2025-05-28 08:02:27.661 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:02:27.662 - New database connection created successfully for thread 11724
2025-05-28 08:02:27.665 - Database connection closed for thread 11724
2025-05-28 08:02:27.666 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:02:27.668 - New database connection created successfully for thread 11724
2025-05-28 08:08:29.485 - Recreating expired connection for thread 12932
2025-05-28 08:08:29.537 - Database connection closed for thread 11724
2025-05-28 08:08:29.537 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:08:29.538 - New database connection created successfully for thread 11724
2025-05-28 08:08:29.541 - Database connection closed for thread 11724
2025-05-28 08:08:29.542 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:08:29.542 - New database connection created successfully for thread 11724
2025-05-28 08:19:53.376 - Recreating expired connection for thread 12932
2025-05-28 08:19:53.433 - Database connection closed for thread 11724
2025-05-28 08:19:53.434 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:19:53.435 - New database connection created successfully for thread 11724
2025-05-28 08:19:53.437 - Database connection closed for thread 11724
2025-05-28 08:19:53.438 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:19:53.438 - New database connection created successfully for thread 11724
2025-05-28 08:25:51.625 - Recreating expired connection for thread 12932
2025-05-28 08:25:51.653 - Database connection closed for thread 11724
2025-05-28 08:25:51.654 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:25:51.655 - New database connection created successfully for thread 11724
2025-05-28 08:25:51.661 - Database connection closed for thread 11724
2025-05-28 08:25:51.663 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:25:51.666 - New database connection created successfully for thread 11724
2025-05-28 08:36:45.282 - Recreating expired connection for thread 12932
2025-05-28 08:36:45.302 - Recreating expired connection for thread 11724
2025-05-28 08:36:45.358 - Database connection closed for thread 11724
2025-05-28 08:36:45.358 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:36:45.359 - New database connection created successfully for thread 11724
2025-05-28 08:36:45.371 - Database connection closed for thread 11724
2025-05-28 08:36:45.372 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:36:45.375 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.166 - Recreating expired connection for thread 11724
2025-05-28 08:50:01.237 - Database connection closed for thread 11724
2025-05-28 08:50:01.249 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.250 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.251 - Database connection closed for thread 11724
2025-05-28 08:50:01.251 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.252 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.261 - Database connection closed for thread 11724
2025-05-28 08:50:01.263 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.263 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.269 - Database connection closed for thread 11724
2025-05-28 08:50:01.270 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.272 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.276 - Database connection closed for thread 11724
2025-05-28 08:50:01.277 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.278 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.294 - Database connection closed for thread 11724
2025-05-28 08:50:01.294 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.295 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.297 - Database connection closed for thread 11724
2025-05-28 08:50:01.298 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.299 - New database connection created successfully for thread 11724
2025-05-28 08:50:11.511 - Recreating expired connection for thread 12932
2025-05-28 08:50:11.595 - Using existing connection for thread 11724
2025-05-28 08:50:11.734 - Database connection closed for thread 11724
2025-05-28 08:50:11.734 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:11.737 - New database connection created successfully for thread 11724
2025-05-28 08:50:11.746 - Database connection closed for thread 11724
2025-05-28 08:50:11.749 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:11.751 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.245 - Recreating expired connection for thread 11724
2025-05-28 08:56:15.414 - Database connection closed for thread 11724
2025-05-28 08:56:15.434 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.437 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.444 - Database connection closed for thread 11724
2025-05-28 08:56:15.470 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.473 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.494 - Database connection closed for thread 11724
2025-05-28 08:56:15.498 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.508 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.559 - Database connection closed for thread 11724
2025-05-28 08:56:15.578 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.584 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.599 - Database connection closed for thread 11724
2025-05-28 08:56:15.608 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.617 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.708 - Database connection closed for thread 11724
2025-05-28 08:56:15.713 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.715 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.747 - Database connection closed for thread 11724
2025-05-28 08:56:15.755 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.762 - New database connection created successfully for thread 11724
2025-05-28 08:56:26.337 - Recreating expired connection for thread 12932
2025-05-28 08:56:26.406 - Using existing connection for thread 11724
2025-05-28 08:56:26.504 - Database connection closed for thread 11724
2025-05-28 08:56:26.505 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:26.505 - New database connection created successfully for thread 11724
2025-05-28 08:56:26.516 - Database connection closed for thread 11724
2025-05-28 08:56:26.518 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:26.519 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.888 - Recreating expired connection for thread 11724
2025-05-28 09:29:17.940 - Database connection closed for thread 11724
2025-05-28 09:29:17.956 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.957 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.958 - Database connection closed for thread 11724
2025-05-28 09:29:17.958 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.959 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.962 - Database connection closed for thread 11724
2025-05-28 09:29:17.962 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.965 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.973 - Database connection closed for thread 11724
2025-05-28 09:29:17.973 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.975 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.980 - Database connection closed for thread 11724
2025-05-28 09:29:17.981 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.981 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.996 - Database connection closed for thread 11724
2025-05-28 09:29:17.997 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.997 - New database connection created successfully for thread 11724
2025-05-28 09:29:18.002 - Database connection closed for thread 11724
2025-05-28 09:29:18.003 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:18.004 - New database connection created successfully for thread 11724
2025-05-28 11:24:03.571 - ensure_database_exists called from thread 128
2025-05-28 11:24:03.572 - Creating new thread-specific database connection to data\stats.db for thread 128
2025-05-28 11:24:03.573 - New database connection created successfully for thread 128
2025-05-28 11:24:03.576 - Stats database initialized successfully
2025-05-28 11:24:03.577 - ensure_database_exists called from thread 128
2025-05-28 11:24:03.577 - Using existing connection for thread 128
2025-05-28 11:24:03.578 - Stats database initialized successfully
2025-05-28 11:24:03.579 - ensure_database_exists called from thread 128
2025-05-28 11:24:03.580 - Using existing connection for thread 128
2025-05-28 11:24:03.581 - Stats database initialized successfully
2025-05-29 02:36:09.448 - ensure_database_exists called from thread 2576
2025-05-29 02:36:09.495 - Creating new thread-specific database connection to data\stats.db for thread 2576
2025-05-29 02:36:09.495 - New database connection created successfully for thread 2576
2025-05-29 02:36:09.498 - Stats database initialized successfully
2025-05-29 02:36:09.501 - ensure_database_exists called from thread 2576
2025-05-29 02:36:09.502 - Using existing connection for thread 2576
2025-05-29 02:36:09.503 - Stats database initialized successfully
2025-05-29 02:36:09.503 - ensure_database_exists called from thread 2576
2025-05-29 02:36:09.504 - Using existing connection for thread 2576
2025-05-29 02:36:09.505 - Stats database initialized successfully
2025-05-29 03:28:58.382 - ensure_database_exists called from thread 11540
2025-05-29 03:28:58.396 - Creating new thread-specific database connection to data\stats.db for thread 11540
2025-05-29 03:28:58.423 - New database connection created successfully for thread 11540
2025-05-29 03:28:58.438 - Stats database initialized successfully
2025-05-29 03:28:58.438 - ensure_database_exists called from thread 11540
2025-05-29 03:28:58.439 - Using existing connection for thread 11540
2025-05-29 03:28:58.440 - Stats database initialized successfully
2025-05-29 03:28:58.441 - ensure_database_exists called from thread 11540
2025-05-29 03:28:58.441 - Using existing connection for thread 11540
2025-05-29 03:28:58.442 - Stats database initialized successfully
2025-05-29 03:30:01.773 - ensure_database_exists called from thread 14684
2025-05-29 03:30:01.774 - Creating new thread-specific database connection to data\stats.db for thread 14684
2025-05-29 03:30:01.775 - New database connection created successfully for thread 14684
2025-05-29 03:30:01.777 - Stats database initialized successfully
2025-05-29 03:30:01.780 - ensure_database_exists called from thread 14684
2025-05-29 03:30:01.781 - Using existing connection for thread 14684
2025-05-29 03:30:01.782 - Stats database initialized successfully
2025-05-29 03:30:02.438 - ensure_database_exists called from thread 14684
2025-05-29 03:30:02.438 - Using existing connection for thread 14684
2025-05-29 03:30:02.439 - Stats database initialized successfully
2025-05-29 03:32:18.412 - ensure_database_exists called from thread 5100
2025-05-29 03:32:18.413 - Creating new thread-specific database connection to data\stats.db for thread 5100
2025-05-29 03:32:18.414 - New database connection created successfully for thread 5100
2025-05-29 03:32:18.416 - Stats database initialized successfully
2025-05-29 03:32:18.417 - ensure_database_exists called from thread 5100
2025-05-29 03:32:18.420 - Using existing connection for thread 5100
2025-05-29 03:32:18.421 - Stats database initialized successfully
2025-05-29 03:32:18.422 - ensure_database_exists called from thread 5100
2025-05-29 03:32:18.423 - Using existing connection for thread 5100
2025-05-29 03:32:18.423 - Stats database initialized successfully
2025-05-29 03:40:33.016 - ensure_database_exists called from thread 9368
2025-05-29 03:40:33.089 - Creating new thread-specific database connection to data\stats.db for thread 9368
2025-05-29 03:40:33.089 - New database connection created successfully for thread 9368
2025-05-29 03:40:33.203 - Stats database initialized successfully
2025-05-29 03:40:33.234 - ensure_database_exists called from thread 9368
2025-05-29 03:40:33.235 - Using existing connection for thread 9368
2025-05-29 03:40:33.236 - Stats database initialized successfully
2025-05-29 03:40:33.236 - ensure_database_exists called from thread 9368
2025-05-29 03:40:33.237 - Using existing connection for thread 9368
2025-05-29 03:40:33.237 - Stats database initialized successfully
2025-05-29 10:34:04.419 - ensure_database_exists called from thread 11872
2025-05-29 10:34:04.809 - Creating new thread-specific database connection to data\stats.db for thread 11872
2025-05-29 10:34:04.810 - New database connection created successfully for thread 11872
2025-05-29 10:34:04.811 - Stats database initialized successfully
2025-05-29 10:34:04.813 - ensure_database_exists called from thread 11872
2025-05-29 10:34:04.813 - Using existing connection for thread 11872
2025-05-29 10:34:04.814 - Stats database initialized successfully
2025-05-29 10:34:04.816 - ensure_database_exists called from thread 11872
2025-05-29 10:34:04.817 - Using existing connection for thread 11872
2025-05-29 10:34:04.818 - Stats database initialized successfully
2025-05-29 10:36:03.264 - Using existing connection for thread 11872
2025-05-29 10:36:03.343 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:36:03.348 - New database connection created successfully for thread 5664
2025-05-29 10:36:04.202 - Database connection closed for thread 5664
2025-05-29 10:36:04.213 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:36:04.215 - New database connection created successfully for thread 5664
2025-05-29 10:36:04.224 - Database connection closed for thread 5664
2025-05-29 10:36:04.245 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:36:04.246 - New database connection created successfully for thread 5664
2025-05-29 10:36:04.629 - get_summary_stats called from thread 5664
2025-05-29 10:36:04.630 - Using existing connection for thread 5664
2025-05-29 10:36:04.631 - Total earnings from database: 382.0
2025-05-29 10:36:04.633 - Daily earnings from database: 0.0
2025-05-29 10:36:04.634 - Daily games from database: 0
2025-05-29 10:36:04.636 - Wallet balance from database: 0
2025-05-29 10:36:04.638 - Total games played from database: 8
2025-05-29 10:36:04.639 - Total winners from database: 8
2025-05-29 10:36:04.643 - Returning summary stats: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:41:10.231 - Recreating expired connection for thread 5664
2025-05-29 10:41:10.246 - ensure_database_exists called from thread 11872
2025-05-29 10:41:10.247 - Recreating expired connection for thread 11872
2025-05-29 10:41:10.249 - Stats database initialized successfully
2025-05-29 10:41:13.582 - Database connection closed for thread 5664
2025-05-29 10:41:13.588 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.589 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.592 - Database connection closed for thread 5664
2025-05-29 10:41:13.593 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.595 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.599 - Database connection closed for thread 5664
2025-05-29 10:41:13.600 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.601 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.780 - Database connection closed for thread 5664
2025-05-29 10:41:13.784 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.786 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.798 - Database connection closed for thread 5664
2025-05-29 10:41:13.799 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.800 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.834 - Database connection closed for thread 5664
2025-05-29 10:41:13.835 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.837 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.846 - Database connection closed for thread 5664
2025-05-29 10:41:13.847 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.848 - New database connection created successfully for thread 5664
