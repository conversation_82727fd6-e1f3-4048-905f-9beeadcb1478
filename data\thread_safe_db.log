2025-05-27 11:35:25.350 - ensure_database_exists called from thread 14364
2025-05-27 11:35:25.353 - Creating new thread-specific database connection to data\stats.db for thread 14364
2025-05-27 11:35:25.354 - New database connection created successfully for thread 14364
2025-05-27 11:35:25.394 - Stats database initialized successfully
2025-05-27 11:35:25.395 - ensure_database_exists called from thread 14364
2025-05-27 11:35:25.396 - Using existing connection for thread 14364
2025-05-27 11:35:25.399 - Stats database initialized successfully
2025-05-27 11:35:25.401 - ensure_database_exists called from thread 14364
2025-05-27 11:35:25.404 - Using existing connection for thread 14364
2025-05-27 11:35:25.404 - Stats database initialized successfully
2025-05-27 11:55:24.219 - ensure_database_exists called from thread 12412
2025-05-27 11:55:24.221 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-05-27 11:55:24.224 - New database connection created successfully for thread 12412
2025-05-27 11:55:24.226 - Stats database initialized successfully
2025-05-27 11:55:24.227 - ensure_database_exists called from thread 12412
2025-05-27 11:55:24.228 - Using existing connection for thread 12412
2025-05-27 11:55:24.229 - Stats database initialized successfully
2025-05-27 11:55:24.229 - ensure_database_exists called from thread 12412
2025-05-27 11:55:24.231 - Using existing connection for thread 12412
2025-05-27 11:55:24.232 - Stats database initialized successfully
2025-05-27 11:57:27.341 - ensure_database_exists called from thread 13388
2025-05-27 11:57:27.342 - Creating new thread-specific database connection to data\stats.db for thread 13388
2025-05-27 11:57:27.343 - New database connection created successfully for thread 13388
2025-05-27 11:57:27.345 - Stats database initialized successfully
2025-05-27 11:57:27.346 - ensure_database_exists called from thread 13388
2025-05-27 11:57:27.347 - Using existing connection for thread 13388
2025-05-27 11:57:27.349 - Stats database initialized successfully
2025-05-27 11:57:27.350 - ensure_database_exists called from thread 13388
2025-05-27 11:57:27.351 - Using existing connection for thread 13388
2025-05-27 11:57:27.351 - Stats database initialized successfully
2025-05-27 12:00:19.550 - ensure_database_exists called from thread 15092
2025-05-27 12:00:19.552 - Creating new thread-specific database connection to data\stats.db for thread 15092
2025-05-27 12:00:19.553 - New database connection created successfully for thread 15092
2025-05-27 12:00:19.556 - Stats database initialized successfully
2025-05-27 12:00:19.557 - ensure_database_exists called from thread 15092
2025-05-27 12:00:19.558 - Using existing connection for thread 15092
2025-05-27 12:00:19.558 - Stats database initialized successfully
2025-05-27 12:00:19.560 - ensure_database_exists called from thread 15092
2025-05-27 12:00:19.560 - Using existing connection for thread 15092
2025-05-27 12:00:19.561 - Stats database initialized successfully
2025-05-27 12:01:39.337 - ensure_database_exists called from thread 8464
2025-05-27 12:01:39.338 - Creating new thread-specific database connection to data\stats.db for thread 8464
2025-05-27 12:01:39.339 - New database connection created successfully for thread 8464
2025-05-27 12:01:39.343 - Stats database initialized successfully
2025-05-27 12:01:39.343 - ensure_database_exists called from thread 8464
2025-05-27 12:01:39.345 - Using existing connection for thread 8464
2025-05-27 12:01:39.346 - Stats database initialized successfully
2025-05-27 12:01:39.347 - ensure_database_exists called from thread 8464
2025-05-27 12:01:39.348 - Using existing connection for thread 8464
2025-05-27 12:01:39.349 - Stats database initialized successfully
2025-05-27 12:03:22.044 - ensure_database_exists called from thread 7980
2025-05-27 12:03:22.046 - Creating new thread-specific database connection to data\stats.db for thread 7980
2025-05-27 12:03:22.047 - New database connection created successfully for thread 7980
2025-05-27 12:03:22.050 - Stats database initialized successfully
2025-05-27 12:03:22.051 - ensure_database_exists called from thread 7980
2025-05-27 12:03:22.052 - Using existing connection for thread 7980
2025-05-27 12:03:22.053 - Stats database initialized successfully
2025-05-27 12:03:22.055 - ensure_database_exists called from thread 7980
2025-05-27 12:03:22.056 - Using existing connection for thread 7980
2025-05-27 12:03:22.057 - Stats database initialized successfully
2025-05-27 12:06:49.953 - ensure_database_exists called from thread 15212
2025-05-27 12:06:49.953 - Creating new thread-specific database connection to data\stats.db for thread 15212
2025-05-27 12:06:49.953 - New database connection created successfully for thread 15212
2025-05-27 12:06:49.953 - Stats database initialized successfully
2025-05-27 12:06:49.961 - ensure_database_exists called from thread 15212
2025-05-27 12:06:49.968 - Using existing connection for thread 15212
2025-05-27 12:06:49.969 - Stats database initialized successfully
2025-05-27 12:07:27.578 - ensure_database_exists called from thread 15280
2025-05-27 12:07:27.578 - Creating new thread-specific database connection to data\stats.db for thread 15280
2025-05-27 12:07:27.586 - New database connection created successfully for thread 15280
2025-05-27 12:07:27.586 - Stats database initialized successfully
2025-05-27 12:07:27.586 - ensure_database_exists called from thread 15280
2025-05-27 12:07:27.586 - Using existing connection for thread 15280
2025-05-27 12:07:27.586 - Stats database initialized successfully
2025-05-27 12:09:41.546 - ensure_database_exists called from thread 9992
2025-05-27 12:09:41.552 - Creating new thread-specific database connection to data\stats.db for thread 9992
2025-05-27 12:09:41.553 - New database connection created successfully for thread 9992
2025-05-27 12:09:41.555 - Stats database initialized successfully
2025-05-27 12:09:41.556 - ensure_database_exists called from thread 9992
2025-05-27 12:09:41.557 - Using existing connection for thread 9992
2025-05-27 12:09:41.558 - Stats database initialized successfully
2025-05-27 12:09:41.559 - ensure_database_exists called from thread 9992
2025-05-27 12:09:41.560 - Using existing connection for thread 9992
2025-05-27 12:09:41.562 - Stats database initialized successfully
2025-05-27 12:12:52.972 - ensure_database_exists called from thread 12412
2025-05-27 12:12:52.982 - Creating new thread-specific database connection to data\stats.db for thread 12412
2025-05-27 12:12:52.988 - New database connection created successfully for thread 12412
2025-05-27 12:12:52.988 - Stats database initialized successfully
2025-05-27 12:12:52.988 - ensure_database_exists called from thread 12412
2025-05-27 12:12:52.988 - Using existing connection for thread 12412
2025-05-27 12:12:52.988 - Stats database initialized successfully
2025-05-27 12:13:59.226 - ensure_database_exists called from thread 13140
2025-05-27 12:13:59.228 - Creating new thread-specific database connection to data\stats.db for thread 13140
2025-05-27 12:13:59.230 - New database connection created successfully for thread 13140
2025-05-27 12:13:59.232 - Stats database initialized successfully
2025-05-27 12:13:59.235 - ensure_database_exists called from thread 13140
2025-05-27 12:13:59.237 - Using existing connection for thread 13140
2025-05-27 12:13:59.238 - Stats database initialized successfully
2025-05-27 12:13:59.239 - ensure_database_exists called from thread 13140
2025-05-27 12:13:59.240 - Using existing connection for thread 13140
2025-05-27 12:13:59.241 - Stats database initialized successfully
2025-05-27 12:14:52.431 - Using existing connection for thread 13140
2025-05-27 12:14:52.450 - Creating new thread-specific database connection to data\stats.db for thread 14656
2025-05-27 12:14:52.451 - New database connection created successfully for thread 14656
2025-05-27 12:14:52.472 - Database connection closed for thread 14656
2025-05-27 12:14:52.481 - Creating new thread-specific database connection to data\stats.db for thread 14656
2025-05-27 12:14:52.484 - New database connection created successfully for thread 14656
2025-05-27 12:14:52.517 - get_summary_stats called from thread 14656
2025-05-27 12:14:52.517 - Using existing connection for thread 14656
2025-05-27 12:14:52.520 - Total earnings from database: 0
2025-05-27 12:14:52.522 - Daily earnings from database: 0
2025-05-27 12:14:52.523 - Daily games from database: 0
2025-05-27 12:14:52.527 - Wallet balance from database: 0
2025-05-27 12:14:52.531 - Total games played from database: 0
2025-05-27 12:14:52.532 - Total winners from database: 0
2025-05-27 12:14:52.533 - Returning summary stats: {'total_earnings': 0, 'daily_earnings': 0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 12:14:59.149 - ensure_database_exists called from thread 13140
2025-05-27 12:14:59.149 - Using existing connection for thread 13140
2025-05-27 12:14:59.151 - Stats database initialized successfully
2025-05-27 12:18:30.234 - ensure_database_exists called from thread 2608
2025-05-27 12:18:30.235 - Creating new thread-specific database connection to data\stats.db for thread 2608
2025-05-27 12:18:30.237 - New database connection created successfully for thread 2608
2025-05-27 12:18:30.241 - Stats database initialized successfully
2025-05-27 12:18:30.242 - ensure_database_exists called from thread 2608
2025-05-27 12:18:30.244 - Using existing connection for thread 2608
2025-05-27 12:18:30.245 - Stats database initialized successfully
2025-05-27 12:18:30.246 - ensure_database_exists called from thread 2608
2025-05-27 12:18:30.247 - Using existing connection for thread 2608
2025-05-27 12:18:30.248 - Stats database initialized successfully
2025-05-27 12:22:01.819 - ensure_database_exists called from thread 3268
2025-05-27 12:22:01.819 - Creating new thread-specific database connection to data\stats.db for thread 3268
2025-05-27 12:22:01.819 - New database connection created successfully for thread 3268
2025-05-27 12:22:01.827 - Stats database initialized successfully
2025-05-27 12:22:01.827 - ensure_database_exists called from thread 3268
2025-05-27 12:22:01.835 - Using existing connection for thread 3268
2025-05-27 12:22:01.835 - Stats database initialized successfully
2025-05-27 12:22:32.006 - ensure_database_exists called from thread 8620
2025-05-27 12:22:32.014 - Creating new thread-specific database connection to data\stats.db for thread 8620
2025-05-27 12:22:32.014 - New database connection created successfully for thread 8620
2025-05-27 12:22:32.014 - Stats database initialized successfully
2025-05-27 12:22:32.014 - ensure_database_exists called from thread 8620
2025-05-27 12:22:32.022 - Using existing connection for thread 8620
2025-05-27 12:22:32.022 - Stats database initialized successfully
2025-05-27 12:24:02.157 - ensure_database_exists called from thread 8468
2025-05-27 12:24:02.158 - Creating new thread-specific database connection to data\stats.db for thread 8468
2025-05-27 12:24:02.160 - New database connection created successfully for thread 8468
2025-05-27 12:24:02.162 - Stats database initialized successfully
2025-05-27 12:24:02.163 - ensure_database_exists called from thread 8468
2025-05-27 12:24:02.164 - Using existing connection for thread 8468
2025-05-27 12:24:02.165 - Stats database initialized successfully
2025-05-27 12:24:02.167 - ensure_database_exists called from thread 8468
2025-05-27 12:24:02.168 - Using existing connection for thread 8468
2025-05-27 12:24:02.169 - Stats database initialized successfully
2025-05-27 12:26:07.987 - ensure_database_exists called from thread 4456
2025-05-27 12:26:07.995 - Creating new thread-specific database connection to data\stats.db for thread 4456
2025-05-27 12:26:07.995 - New database connection created successfully for thread 4456
2025-05-27 12:26:08.008 - Stats database initialized successfully
2025-05-27 12:26:08.011 - ensure_database_exists called from thread 4456
2025-05-27 12:26:08.011 - Using existing connection for thread 4456
2025-05-27 12:26:08.019 - Stats database initialized successfully
2025-05-27 12:27:11.806 - ensure_database_exists called from thread 9984
2025-05-27 12:27:11.814 - Creating new thread-specific database connection to data\stats.db for thread 9984
2025-05-27 12:27:11.814 - New database connection created successfully for thread 9984
2025-05-27 12:27:11.822 - Stats database initialized successfully
2025-05-27 12:27:11.830 - ensure_database_exists called from thread 9984
2025-05-27 12:27:11.830 - Using existing connection for thread 9984
2025-05-27 12:27:11.830 - Stats database initialized successfully
2025-05-27 12:27:43.125 - ensure_database_exists called from thread 10712
2025-05-27 12:27:43.128 - Creating new thread-specific database connection to data\stats.db for thread 10712
2025-05-27 12:27:43.131 - New database connection created successfully for thread 10712
2025-05-27 12:27:43.137 - Stats database initialized successfully
2025-05-27 12:27:43.139 - ensure_database_exists called from thread 10712
2025-05-27 12:27:43.141 - Using existing connection for thread 10712
2025-05-27 12:27:43.143 - Stats database initialized successfully
2025-05-27 12:28:20.097 - ensure_database_exists called from thread 7320
2025-05-27 12:28:20.105 - Creating new thread-specific database connection to data\stats.db for thread 7320
2025-05-27 12:28:20.105 - New database connection created successfully for thread 7320
2025-05-27 12:28:20.108 - Stats database initialized successfully
2025-05-27 12:28:20.112 - ensure_database_exists called from thread 7320
2025-05-27 12:28:20.116 - Using existing connection for thread 7320
2025-05-27 12:28:20.120 - Stats database initialized successfully
2025-05-27 12:30:02.765 - ensure_database_exists called from thread 12472
2025-05-27 12:30:02.766 - Creating new thread-specific database connection to data\stats.db for thread 12472
2025-05-27 12:30:02.767 - New database connection created successfully for thread 12472
2025-05-27 12:30:02.769 - Stats database initialized successfully
2025-05-27 12:30:02.770 - ensure_database_exists called from thread 12472
2025-05-27 12:30:02.770 - Using existing connection for thread 12472
2025-05-27 12:30:02.771 - Stats database initialized successfully
2025-05-27 12:30:02.772 - ensure_database_exists called from thread 12472
2025-05-27 12:30:02.773 - Using existing connection for thread 12472
2025-05-27 12:30:02.774 - Stats database initialized successfully
2025-05-27 12:32:46.493 - ensure_database_exists called from thread 13936
2025-05-27 12:32:46.495 - Creating new thread-specific database connection to data\stats.db for thread 13936
2025-05-27 12:32:46.495 - New database connection created successfully for thread 13936
2025-05-27 12:32:46.499 - Stats database initialized successfully
2025-05-27 12:32:46.500 - ensure_database_exists called from thread 13936
2025-05-27 12:32:46.501 - Using existing connection for thread 13936
2025-05-27 12:32:46.502 - Stats database initialized successfully
2025-05-27 12:32:46.503 - ensure_database_exists called from thread 13936
2025-05-27 12:32:46.503 - Using existing connection for thread 13936
2025-05-27 12:32:46.504 - Stats database initialized successfully
2025-05-27 12:35:25.872 - ensure_database_exists called from thread 11960
2025-05-27 12:35:25.873 - Creating new thread-specific database connection to data\stats.db for thread 11960
2025-05-27 12:35:25.875 - New database connection created successfully for thread 11960
2025-05-27 12:35:25.878 - Stats database initialized successfully
2025-05-27 12:35:25.879 - ensure_database_exists called from thread 11960
2025-05-27 12:35:25.880 - Using existing connection for thread 11960
2025-05-27 12:35:25.880 - Stats database initialized successfully
2025-05-27 12:35:25.882 - ensure_database_exists called from thread 11960
2025-05-27 12:35:25.883 - Using existing connection for thread 11960
2025-05-27 12:35:25.883 - Stats database initialized successfully
2025-05-27 12:44:45.107 - ensure_database_exists called from thread 8896
2025-05-27 12:44:45.109 - Creating new thread-specific database connection to data\stats.db for thread 8896
2025-05-27 12:44:45.110 - New database connection created successfully for thread 8896
2025-05-27 12:44:45.319 - Stats database initialized successfully
2025-05-27 12:44:45.323 - ensure_database_exists called from thread 8896
2025-05-27 12:44:45.324 - Using existing connection for thread 8896
2025-05-27 12:44:45.325 - Stats database initialized successfully
2025-05-27 12:51:26.984 - ensure_database_exists called from thread 6480
2025-05-27 12:51:26.990 - Creating new thread-specific database connection to data\stats.db for thread 6480
2025-05-27 12:51:26.991 - New database connection created successfully for thread 6480
2025-05-27 12:51:26.998 - Stats database initialized successfully
2025-05-27 12:51:26.999 - ensure_database_exists called from thread 6480
2025-05-27 12:51:27.000 - Using existing connection for thread 6480
2025-05-27 12:51:27.001 - Stats database initialized successfully
2025-05-27 12:52:48.548 - ensure_database_exists called from thread 12160
2025-05-27 12:52:48.550 - Creating new thread-specific database connection to data\stats.db for thread 12160
2025-05-27 12:52:48.552 - New database connection created successfully for thread 12160
2025-05-27 12:52:48.554 - Stats database initialized successfully
2025-05-27 12:52:48.555 - ensure_database_exists called from thread 12160
2025-05-27 12:52:48.556 - Using existing connection for thread 12160
2025-05-27 12:52:48.557 - Stats database initialized successfully
2025-05-27 12:52:48.558 - ensure_database_exists called from thread 12160
2025-05-27 12:52:48.558 - Using existing connection for thread 12160
2025-05-27 12:52:48.559 - Stats database initialized successfully
2025-05-27 12:54:50.572 - ensure_database_exists called from thread 10232
2025-05-27 12:54:50.576 - Creating new thread-specific database connection to data\stats.db for thread 10232
2025-05-27 12:54:50.578 - New database connection created successfully for thread 10232
2025-05-27 12:54:50.580 - Stats database initialized successfully
2025-05-27 12:54:50.581 - ensure_database_exists called from thread 10232
2025-05-27 12:54:50.581 - Using existing connection for thread 10232
2025-05-27 12:54:50.582 - Stats database initialized successfully
2025-05-27 12:57:59.435 - ensure_database_exists called from thread 10008
2025-05-27 12:57:59.436 - Creating new thread-specific database connection to data\stats.db for thread 10008
2025-05-27 12:57:59.438 - New database connection created successfully for thread 10008
2025-05-27 12:57:59.441 - Stats database initialized successfully
2025-05-27 12:57:59.442 - ensure_database_exists called from thread 10008
2025-05-27 12:57:59.444 - Using existing connection for thread 10008
2025-05-27 12:57:59.445 - Stats database initialized successfully
2025-05-27 12:58:14.467 - ensure_database_exists called from thread 14656
2025-05-27 12:58:14.470 - Creating new thread-specific database connection to data\stats.db for thread 14656
2025-05-27 12:58:14.473 - New database connection created successfully for thread 14656
2025-05-27 12:58:14.475 - Stats database initialized successfully
2025-05-27 12:58:14.476 - ensure_database_exists called from thread 14656
2025-05-27 12:58:14.477 - Using existing connection for thread 14656
2025-05-27 12:58:14.478 - Stats database initialized successfully
2025-05-27 12:58:14.480 - ensure_database_exists called from thread 14656
2025-05-27 12:58:14.480 - Using existing connection for thread 14656
2025-05-27 12:58:14.481 - Stats database initialized successfully
2025-05-27 13:10:06.268 - ensure_database_exists called from thread 7968
2025-05-27 13:10:06.269 - Creating new thread-specific database connection to data\stats.db for thread 7968
2025-05-27 13:10:06.270 - New database connection created successfully for thread 7968
2025-05-27 13:10:06.274 - Stats database initialized successfully
2025-05-27 13:10:06.275 - ensure_database_exists called from thread 7968
2025-05-27 13:10:06.276 - Using existing connection for thread 7968
2025-05-27 13:10:06.278 - Stats database initialized successfully
2025-05-27 13:14:27.587 - ensure_database_exists called from thread 2620
2025-05-27 13:14:27.589 - Creating new thread-specific database connection to data\stats.db for thread 2620
2025-05-27 13:14:27.591 - New database connection created successfully for thread 2620
2025-05-27 13:14:27.593 - Stats database initialized successfully
2025-05-27 13:14:27.594 - ensure_database_exists called from thread 2620
2025-05-27 13:14:27.595 - Using existing connection for thread 2620
2025-05-27 13:14:27.596 - Stats database initialized successfully
2025-05-27 13:14:59.765 - ensure_database_exists called from thread 12140
2025-05-27 13:14:59.767 - Creating new thread-specific database connection to data\stats.db for thread 12140
2025-05-27 13:14:59.768 - New database connection created successfully for thread 12140
2025-05-27 13:14:59.776 - Stats database initialized successfully
2025-05-27 13:14:59.777 - ensure_database_exists called from thread 12140
2025-05-27 13:14:59.778 - Using existing connection for thread 12140
2025-05-27 13:14:59.779 - Stats database initialized successfully
2025-05-27 13:21:59.164 - ensure_database_exists called from thread 8312
2025-05-27 13:21:59.165 - Creating new thread-specific database connection to data\stats.db for thread 8312
2025-05-27 13:21:59.167 - New database connection created successfully for thread 8312
2025-05-27 13:21:59.169 - Stats database initialized successfully
2025-05-27 13:21:59.169 - ensure_database_exists called from thread 8312
2025-05-27 13:21:59.170 - Using existing connection for thread 8312
2025-05-27 13:21:59.170 - Stats database initialized successfully
2025-05-27 13:21:59.171 - ensure_database_exists called from thread 8312
2025-05-27 13:21:59.171 - Using existing connection for thread 8312
2025-05-27 13:21:59.172 - Stats database initialized successfully
2025-05-27 13:30:49.707 - ensure_database_exists called from thread 12056
2025-05-27 13:30:49.708 - Creating new thread-specific database connection to data\stats.db for thread 12056
2025-05-27 13:30:49.709 - New database connection created successfully for thread 12056
2025-05-27 13:30:49.710 - Stats database initialized successfully
2025-05-27 13:30:49.711 - ensure_database_exists called from thread 12056
2025-05-27 13:30:49.712 - Using existing connection for thread 12056
2025-05-27 13:30:49.713 - Stats database initialized successfully
2025-05-27 13:30:49.714 - ensure_database_exists called from thread 12056
2025-05-27 13:30:49.714 - Using existing connection for thread 12056
2025-05-27 13:30:49.715 - Stats database initialized successfully
2025-05-27 13:33:12.033 - ensure_database_exists called from thread 2420
2025-05-27 13:33:12.034 - Creating new thread-specific database connection to data\stats.db for thread 2420
2025-05-27 13:33:12.035 - New database connection created successfully for thread 2420
2025-05-27 13:33:12.036 - Stats database initialized successfully
2025-05-27 13:33:12.037 - ensure_database_exists called from thread 2420
2025-05-27 13:33:12.037 - Using existing connection for thread 2420
2025-05-27 13:33:12.038 - Stats database initialized successfully
2025-05-27 13:33:12.038 - ensure_database_exists called from thread 2420
2025-05-27 13:33:12.039 - Using existing connection for thread 2420
2025-05-27 13:33:12.039 - Stats database initialized successfully
2025-05-27 13:36:10.471 - ensure_database_exists called from thread 11900
2025-05-27 13:36:10.473 - Creating new thread-specific database connection to data\stats.db for thread 11900
2025-05-27 13:36:10.475 - New database connection created successfully for thread 11900
2025-05-27 13:36:10.477 - Stats database initialized successfully
2025-05-27 13:36:10.478 - ensure_database_exists called from thread 11900
2025-05-27 13:36:10.479 - Using existing connection for thread 11900
2025-05-27 13:36:10.480 - Stats database initialized successfully
2025-05-27 13:36:10.481 - ensure_database_exists called from thread 11900
2025-05-27 13:36:10.482 - Using existing connection for thread 11900
2025-05-27 13:36:10.487 - Stats database initialized successfully
2025-05-27 13:37:03.279 - Using existing connection for thread 11900
2025-05-27 13:37:03.312 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:37:03.313 - New database connection created successfully for thread 12792
2025-05-27 13:37:03.452 - Database connection closed for thread 12792
2025-05-27 13:37:03.458 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:37:03.461 - New database connection created successfully for thread 12792
2025-05-27 13:37:03.468 - Database connection closed for thread 12792
2025-05-27 13:37:03.473 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:37:03.477 - New database connection created successfully for thread 12792
2025-05-27 13:37:03.493 - get_summary_stats called from thread 12792
2025-05-27 13:37:03.495 - Using existing connection for thread 12792
2025-05-27 13:37:03.498 - Total earnings from database: 0.0
2025-05-27 13:37:03.500 - Daily earnings from database: 0.0
2025-05-27 13:37:03.507 - Daily games from database: 0
2025-05-27 13:37:03.508 - Wallet balance from database: 0
2025-05-27 13:37:03.510 - Total games played from database: 0
2025-05-27 13:37:03.513 - Total winners from database: 0
2025-05-27 13:37:03.519 - Returning summary stats: {'total_earnings': 0.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-27 13:38:08.630 - Using existing connection for thread 12792
2025-05-27 13:38:08.661 - ensure_database_exists called from thread 11900
2025-05-27 13:38:08.662 - Using existing connection for thread 11900
2025-05-27 13:38:08.663 - Stats database initialized successfully
2025-05-27 13:38:11.660 - Database connection closed for thread 12792
2025-05-27 13:38:11.694 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.696 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.715 - Database connection closed for thread 12792
2025-05-27 13:38:11.717 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.718 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.729 - Database connection closed for thread 12792
2025-05-27 13:38:11.731 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.733 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.748 - Database connection closed for thread 12792
2025-05-27 13:38:11.749 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.751 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.761 - Database connection closed for thread 12792
2025-05-27 13:38:11.763 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.764 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.783 - Database connection closed for thread 12792
2025-05-27 13:38:11.783 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.784 - New database connection created successfully for thread 12792
2025-05-27 13:38:11.792 - Database connection closed for thread 12792
2025-05-27 13:38:11.793 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:38:11.794 - New database connection created successfully for thread 12792
2025-05-27 13:41:18.383 - Using existing connection for thread 11900
2025-05-27 13:41:18.422 - Using existing connection for thread 12792
2025-05-27 13:41:18.480 - Database connection closed for thread 12792
2025-05-27 13:41:18.483 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:41:18.486 - New database connection created successfully for thread 12792
2025-05-27 13:41:18.497 - Database connection closed for thread 12792
2025-05-27 13:41:18.499 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:41:18.501 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.068 - Using existing connection for thread 12792
2025-05-27 13:42:18.133 - Database connection closed for thread 12792
2025-05-27 13:42:18.135 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.136 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.138 - Database connection closed for thread 12792
2025-05-27 13:42:18.139 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.140 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.146 - Database connection closed for thread 12792
2025-05-27 13:42:18.150 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.152 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.158 - Database connection closed for thread 12792
2025-05-27 13:42:18.162 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.163 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.171 - Database connection closed for thread 12792
2025-05-27 13:42:18.172 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.175 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.192 - Database connection closed for thread 12792
2025-05-27 13:42:18.193 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.195 - New database connection created successfully for thread 12792
2025-05-27 13:42:18.202 - Database connection closed for thread 12792
2025-05-27 13:42:18.203 - Creating new thread-specific database connection to data\stats.db for thread 12792
2025-05-27 13:42:18.205 - New database connection created successfully for thread 12792
2025-05-27 13:55:03.086 - ensure_database_exists called from thread 14704
2025-05-27 13:55:03.088 - Creating new thread-specific database connection to data\stats.db for thread 14704
2025-05-27 13:55:03.091 - New database connection created successfully for thread 14704
2025-05-27 13:55:03.094 - Stats database initialized successfully
2025-05-27 13:55:03.095 - ensure_database_exists called from thread 14704
2025-05-27 13:55:03.096 - Using existing connection for thread 14704
2025-05-27 13:55:03.100 - Stats database initialized successfully
2025-05-27 13:55:03.210 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:03.211 - New database connection created successfully for thread 14308
2025-05-27 13:55:04.755 - Database connection closed for thread 14308
2025-05-27 13:55:04.756 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:04.758 - New database connection created successfully for thread 14308
2025-05-27 13:55:04.764 - Database connection closed for thread 14308
2025-05-27 13:55:04.765 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:04.766 - New database connection created successfully for thread 14308
2025-05-27 13:55:05.400 - get_summary_stats called from thread 14308
2025-05-27 13:55:05.403 - Using existing connection for thread 14308
2025-05-27 13:55:05.417 - Total earnings from database: 120.0
2025-05-27 13:55:05.418 - Daily earnings from database: 120.0
2025-05-27 13:55:05.420 - Daily games from database: 2
2025-05-27 13:55:05.422 - Wallet balance from database: 0
2025-05-27 13:55:05.423 - Total games played from database: 2
2025-05-27 13:55:05.429 - Total winners from database: 2
2025-05-27 13:55:05.438 - Returning summary stats: {'total_earnings': 120.0, 'daily_earnings': 120.0, 'daily_games': 2, 'wallet_balance': 0}
2025-05-27 13:55:05.748 - Using existing connection for thread 14308
2025-05-27 13:55:06.034 - Database connection closed for thread 14308
2025-05-27 13:55:06.037 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.038 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.042 - Database connection closed for thread 14308
2025-05-27 13:55:06.043 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.044 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.047 - Database connection closed for thread 14308
2025-05-27 13:55:06.048 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.055 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.066 - Database connection closed for thread 14308
2025-05-27 13:55:06.067 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.070 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.081 - Database connection closed for thread 14308
2025-05-27 13:55:06.082 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.083 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.105 - Database connection closed for thread 14308
2025-05-27 13:55:06.105 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.106 - New database connection created successfully for thread 14308
2025-05-27 13:55:06.115 - Database connection closed for thread 14308
2025-05-27 13:55:06.116 - Creating new thread-specific database connection to data\stats.db for thread 14308
2025-05-27 13:55:06.118 - New database connection created successfully for thread 14308
2025-05-27 13:56:09.174 - ensure_database_exists called from thread 13528
2025-05-27 13:56:09.178 - Creating new thread-specific database connection to data\stats.db for thread 13528
2025-05-27 13:56:09.183 - New database connection created successfully for thread 13528
2025-05-27 13:56:09.189 - Stats database initialized successfully
2025-05-27 13:56:09.194 - Using existing connection for thread 13528
2025-05-27 13:56:09.683 - Database connection closed for thread 13528
2025-05-27 13:57:15.206 - ensure_database_exists called from thread 16148
2025-05-27 13:57:15.222 - Creating new thread-specific database connection to data\stats.db for thread 16148
2025-05-27 13:57:16.193 - New database connection created successfully for thread 16148
2025-05-27 13:57:16.208 - Stats database initialized successfully
2025-05-27 13:57:16.220 - ensure_database_exists called from thread 16148
2025-05-27 13:57:16.224 - Using existing connection for thread 16148
2025-05-27 13:57:16.246 - Stats database initialized successfully
2025-05-27 13:57:16.282 - ensure_database_exists called from thread 16148
2025-05-27 13:57:16.299 - Using existing connection for thread 16148
2025-05-27 13:57:16.338 - Stats database initialized successfully
2025-05-28 06:47:01.928 - ensure_database_exists called from thread 12932
2025-05-28 06:47:02.036 - Creating new thread-specific database connection to data\stats.db for thread 12932
2025-05-28 06:47:02.037 - New database connection created successfully for thread 12932
2025-05-28 06:47:02.089 - Stats database initialized successfully
2025-05-28 06:47:02.090 - ensure_database_exists called from thread 12932
2025-05-28 06:47:02.090 - Using existing connection for thread 12932
2025-05-28 06:47:02.091 - Stats database initialized successfully
2025-05-28 06:47:02.092 - ensure_database_exists called from thread 12932
2025-05-28 06:47:02.092 - Using existing connection for thread 12932
2025-05-28 06:47:02.093 - Stats database initialized successfully
2025-05-28 07:05:28.153 - Recreating expired connection for thread 12932
2025-05-28 07:05:28.201 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:05:28.232 - New database connection created successfully for thread 11724
2025-05-28 07:05:28.332 - Database connection closed for thread 11724
2025-05-28 07:05:28.333 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:05:28.344 - New database connection created successfully for thread 11724
2025-05-28 07:05:28.372 - Database connection closed for thread 11724
2025-05-28 07:05:28.387 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:05:28.388 - New database connection created successfully for thread 11724
2025-05-28 07:05:28.465 - get_summary_stats called from thread 11724
2025-05-28 07:05:28.466 - Using existing connection for thread 11724
2025-05-28 07:05:28.467 - Total earnings from database: 230.0
2025-05-28 07:05:28.468 - Daily earnings from database: 0.0
2025-05-28 07:05:28.471 - Daily games from database: 0
2025-05-28 07:05:28.476 - Wallet balance from database: 0
2025-05-28 07:05:28.480 - Total games played from database: 4
2025-05-28 07:05:28.483 - Total winners from database: 4
2025-05-28 07:05:28.484 - Returning summary stats: {'total_earnings': 230.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-28 07:07:26.523 - Using existing connection for thread 11724
2025-05-28 07:07:26.556 - ensure_database_exists called from thread 12932
2025-05-28 07:07:26.557 - Using existing connection for thread 12932
2025-05-28 07:07:26.558 - Stats database initialized successfully
2025-05-28 07:07:29.413 - Database connection closed for thread 11724
2025-05-28 07:07:29.838 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.840 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.842 - Database connection closed for thread 11724
2025-05-28 07:07:29.843 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.843 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.847 - Database connection closed for thread 11724
2025-05-28 07:07:29.849 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.857 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.910 - Database connection closed for thread 11724
2025-05-28 07:07:29.912 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.913 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.916 - Database connection closed for thread 11724
2025-05-28 07:07:29.918 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.921 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.957 - Database connection closed for thread 11724
2025-05-28 07:07:29.958 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.958 - New database connection created successfully for thread 11724
2025-05-28 07:07:29.963 - Database connection closed for thread 11724
2025-05-28 07:07:29.963 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:07:29.964 - New database connection created successfully for thread 11724
2025-05-28 07:15:37.953 - Recreating expired connection for thread 12932
2025-05-28 07:15:38.050 - Database connection closed for thread 11724
2025-05-28 07:15:38.051 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:15:38.051 - New database connection created successfully for thread 11724
2025-05-28 07:15:38.053 - Database connection closed for thread 11724
2025-05-28 07:15:38.054 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:15:38.056 - New database connection created successfully for thread 11724
2025-05-28 07:23:13.460 - Recreating expired connection for thread 12932
2025-05-28 07:23:13.509 - Database connection closed for thread 11724
2025-05-28 07:23:13.513 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:23:13.515 - New database connection created successfully for thread 11724
2025-05-28 07:23:13.519 - Database connection closed for thread 11724
2025-05-28 07:23:13.520 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:23:13.520 - New database connection created successfully for thread 11724
2025-05-28 07:31:19.251 - Recreating expired connection for thread 12932
2025-05-28 07:31:19.307 - Database connection closed for thread 11724
2025-05-28 07:31:19.308 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:31:19.308 - New database connection created successfully for thread 11724
2025-05-28 07:31:19.310 - Database connection closed for thread 11724
2025-05-28 07:31:19.311 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:31:19.312 - New database connection created successfully for thread 11724
2025-05-28 07:37:37.291 - Recreating expired connection for thread 12932
2025-05-28 07:37:37.320 - Database connection closed for thread 11724
2025-05-28 07:37:37.321 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:37:37.322 - New database connection created successfully for thread 11724
2025-05-28 07:37:37.325 - Database connection closed for thread 11724
2025-05-28 07:37:37.326 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:37:37.328 - New database connection created successfully for thread 11724
2025-05-28 07:47:29.380 - Recreating expired connection for thread 12932
2025-05-28 07:47:29.423 - Database connection closed for thread 11724
2025-05-28 07:47:29.427 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:47:29.432 - New database connection created successfully for thread 11724
2025-05-28 07:47:29.440 - Database connection closed for thread 11724
2025-05-28 07:47:29.447 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:47:29.451 - New database connection created successfully for thread 11724
2025-05-28 07:56:22.199 - Recreating expired connection for thread 12932
2025-05-28 07:56:22.212 - Database connection closed for thread 11724
2025-05-28 07:56:22.213 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:56:22.214 - New database connection created successfully for thread 11724
2025-05-28 07:56:22.221 - Database connection closed for thread 11724
2025-05-28 07:56:22.222 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 07:56:22.224 - New database connection created successfully for thread 11724
2025-05-28 08:02:27.610 - Recreating expired connection for thread 12932
2025-05-28 08:02:27.661 - Database connection closed for thread 11724
2025-05-28 08:02:27.661 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:02:27.662 - New database connection created successfully for thread 11724
2025-05-28 08:02:27.665 - Database connection closed for thread 11724
2025-05-28 08:02:27.666 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:02:27.668 - New database connection created successfully for thread 11724
2025-05-28 08:08:29.485 - Recreating expired connection for thread 12932
2025-05-28 08:08:29.537 - Database connection closed for thread 11724
2025-05-28 08:08:29.537 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:08:29.538 - New database connection created successfully for thread 11724
2025-05-28 08:08:29.541 - Database connection closed for thread 11724
2025-05-28 08:08:29.542 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:08:29.542 - New database connection created successfully for thread 11724
2025-05-28 08:19:53.376 - Recreating expired connection for thread 12932
2025-05-28 08:19:53.433 - Database connection closed for thread 11724
2025-05-28 08:19:53.434 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:19:53.435 - New database connection created successfully for thread 11724
2025-05-28 08:19:53.437 - Database connection closed for thread 11724
2025-05-28 08:19:53.438 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:19:53.438 - New database connection created successfully for thread 11724
2025-05-28 08:25:51.625 - Recreating expired connection for thread 12932
2025-05-28 08:25:51.653 - Database connection closed for thread 11724
2025-05-28 08:25:51.654 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:25:51.655 - New database connection created successfully for thread 11724
2025-05-28 08:25:51.661 - Database connection closed for thread 11724
2025-05-28 08:25:51.663 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:25:51.666 - New database connection created successfully for thread 11724
2025-05-28 08:36:45.282 - Recreating expired connection for thread 12932
2025-05-28 08:36:45.302 - Recreating expired connection for thread 11724
2025-05-28 08:36:45.358 - Database connection closed for thread 11724
2025-05-28 08:36:45.358 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:36:45.359 - New database connection created successfully for thread 11724
2025-05-28 08:36:45.371 - Database connection closed for thread 11724
2025-05-28 08:36:45.372 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:36:45.375 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.166 - Recreating expired connection for thread 11724
2025-05-28 08:50:01.237 - Database connection closed for thread 11724
2025-05-28 08:50:01.249 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.250 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.251 - Database connection closed for thread 11724
2025-05-28 08:50:01.251 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.252 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.261 - Database connection closed for thread 11724
2025-05-28 08:50:01.263 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.263 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.269 - Database connection closed for thread 11724
2025-05-28 08:50:01.270 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.272 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.276 - Database connection closed for thread 11724
2025-05-28 08:50:01.277 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.278 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.294 - Database connection closed for thread 11724
2025-05-28 08:50:01.294 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.295 - New database connection created successfully for thread 11724
2025-05-28 08:50:01.297 - Database connection closed for thread 11724
2025-05-28 08:50:01.298 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:01.299 - New database connection created successfully for thread 11724
2025-05-28 08:50:11.511 - Recreating expired connection for thread 12932
2025-05-28 08:50:11.595 - Using existing connection for thread 11724
2025-05-28 08:50:11.734 - Database connection closed for thread 11724
2025-05-28 08:50:11.734 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:11.737 - New database connection created successfully for thread 11724
2025-05-28 08:50:11.746 - Database connection closed for thread 11724
2025-05-28 08:50:11.749 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:50:11.751 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.245 - Recreating expired connection for thread 11724
2025-05-28 08:56:15.414 - Database connection closed for thread 11724
2025-05-28 08:56:15.434 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.437 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.444 - Database connection closed for thread 11724
2025-05-28 08:56:15.470 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.473 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.494 - Database connection closed for thread 11724
2025-05-28 08:56:15.498 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.508 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.559 - Database connection closed for thread 11724
2025-05-28 08:56:15.578 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.584 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.599 - Database connection closed for thread 11724
2025-05-28 08:56:15.608 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.617 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.708 - Database connection closed for thread 11724
2025-05-28 08:56:15.713 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.715 - New database connection created successfully for thread 11724
2025-05-28 08:56:15.747 - Database connection closed for thread 11724
2025-05-28 08:56:15.755 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:15.762 - New database connection created successfully for thread 11724
2025-05-28 08:56:26.337 - Recreating expired connection for thread 12932
2025-05-28 08:56:26.406 - Using existing connection for thread 11724
2025-05-28 08:56:26.504 - Database connection closed for thread 11724
2025-05-28 08:56:26.505 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:26.505 - New database connection created successfully for thread 11724
2025-05-28 08:56:26.516 - Database connection closed for thread 11724
2025-05-28 08:56:26.518 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 08:56:26.519 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.888 - Recreating expired connection for thread 11724
2025-05-28 09:29:17.940 - Database connection closed for thread 11724
2025-05-28 09:29:17.956 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.957 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.958 - Database connection closed for thread 11724
2025-05-28 09:29:17.958 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.959 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.962 - Database connection closed for thread 11724
2025-05-28 09:29:17.962 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.965 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.973 - Database connection closed for thread 11724
2025-05-28 09:29:17.973 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.975 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.980 - Database connection closed for thread 11724
2025-05-28 09:29:17.981 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.981 - New database connection created successfully for thread 11724
2025-05-28 09:29:17.996 - Database connection closed for thread 11724
2025-05-28 09:29:17.997 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:17.997 - New database connection created successfully for thread 11724
2025-05-28 09:29:18.002 - Database connection closed for thread 11724
2025-05-28 09:29:18.003 - Creating new thread-specific database connection to data\stats.db for thread 11724
2025-05-28 09:29:18.004 - New database connection created successfully for thread 11724
2025-05-28 11:24:03.571 - ensure_database_exists called from thread 128
2025-05-28 11:24:03.572 - Creating new thread-specific database connection to data\stats.db for thread 128
2025-05-28 11:24:03.573 - New database connection created successfully for thread 128
2025-05-28 11:24:03.576 - Stats database initialized successfully
2025-05-28 11:24:03.577 - ensure_database_exists called from thread 128
2025-05-28 11:24:03.577 - Using existing connection for thread 128
2025-05-28 11:24:03.578 - Stats database initialized successfully
2025-05-28 11:24:03.579 - ensure_database_exists called from thread 128
2025-05-28 11:24:03.580 - Using existing connection for thread 128
2025-05-28 11:24:03.581 - Stats database initialized successfully
2025-05-29 02:36:09.448 - ensure_database_exists called from thread 2576
2025-05-29 02:36:09.495 - Creating new thread-specific database connection to data\stats.db for thread 2576
2025-05-29 02:36:09.495 - New database connection created successfully for thread 2576
2025-05-29 02:36:09.498 - Stats database initialized successfully
2025-05-29 02:36:09.501 - ensure_database_exists called from thread 2576
2025-05-29 02:36:09.502 - Using existing connection for thread 2576
2025-05-29 02:36:09.503 - Stats database initialized successfully
2025-05-29 02:36:09.503 - ensure_database_exists called from thread 2576
2025-05-29 02:36:09.504 - Using existing connection for thread 2576
2025-05-29 02:36:09.505 - Stats database initialized successfully
2025-05-29 03:28:58.382 - ensure_database_exists called from thread 11540
2025-05-29 03:28:58.396 - Creating new thread-specific database connection to data\stats.db for thread 11540
2025-05-29 03:28:58.423 - New database connection created successfully for thread 11540
2025-05-29 03:28:58.438 - Stats database initialized successfully
2025-05-29 03:28:58.438 - ensure_database_exists called from thread 11540
2025-05-29 03:28:58.439 - Using existing connection for thread 11540
2025-05-29 03:28:58.440 - Stats database initialized successfully
2025-05-29 03:28:58.441 - ensure_database_exists called from thread 11540
2025-05-29 03:28:58.441 - Using existing connection for thread 11540
2025-05-29 03:28:58.442 - Stats database initialized successfully
2025-05-29 03:30:01.773 - ensure_database_exists called from thread 14684
2025-05-29 03:30:01.774 - Creating new thread-specific database connection to data\stats.db for thread 14684
2025-05-29 03:30:01.775 - New database connection created successfully for thread 14684
2025-05-29 03:30:01.777 - Stats database initialized successfully
2025-05-29 03:30:01.780 - ensure_database_exists called from thread 14684
2025-05-29 03:30:01.781 - Using existing connection for thread 14684
2025-05-29 03:30:01.782 - Stats database initialized successfully
2025-05-29 03:30:02.438 - ensure_database_exists called from thread 14684
2025-05-29 03:30:02.438 - Using existing connection for thread 14684
2025-05-29 03:30:02.439 - Stats database initialized successfully
2025-05-29 03:32:18.412 - ensure_database_exists called from thread 5100
2025-05-29 03:32:18.413 - Creating new thread-specific database connection to data\stats.db for thread 5100
2025-05-29 03:32:18.414 - New database connection created successfully for thread 5100
2025-05-29 03:32:18.416 - Stats database initialized successfully
2025-05-29 03:32:18.417 - ensure_database_exists called from thread 5100
2025-05-29 03:32:18.420 - Using existing connection for thread 5100
2025-05-29 03:32:18.421 - Stats database initialized successfully
2025-05-29 03:32:18.422 - ensure_database_exists called from thread 5100
2025-05-29 03:32:18.423 - Using existing connection for thread 5100
2025-05-29 03:32:18.423 - Stats database initialized successfully
2025-05-29 03:40:33.016 - ensure_database_exists called from thread 9368
2025-05-29 03:40:33.089 - Creating new thread-specific database connection to data\stats.db for thread 9368
2025-05-29 03:40:33.089 - New database connection created successfully for thread 9368
2025-05-29 03:40:33.203 - Stats database initialized successfully
2025-05-29 03:40:33.234 - ensure_database_exists called from thread 9368
2025-05-29 03:40:33.235 - Using existing connection for thread 9368
2025-05-29 03:40:33.236 - Stats database initialized successfully
2025-05-29 03:40:33.236 - ensure_database_exists called from thread 9368
2025-05-29 03:40:33.237 - Using existing connection for thread 9368
2025-05-29 03:40:33.237 - Stats database initialized successfully
2025-05-29 10:34:04.419 - ensure_database_exists called from thread 11872
2025-05-29 10:34:04.809 - Creating new thread-specific database connection to data\stats.db for thread 11872
2025-05-29 10:34:04.810 - New database connection created successfully for thread 11872
2025-05-29 10:34:04.811 - Stats database initialized successfully
2025-05-29 10:34:04.813 - ensure_database_exists called from thread 11872
2025-05-29 10:34:04.813 - Using existing connection for thread 11872
2025-05-29 10:34:04.814 - Stats database initialized successfully
2025-05-29 10:34:04.816 - ensure_database_exists called from thread 11872
2025-05-29 10:34:04.817 - Using existing connection for thread 11872
2025-05-29 10:34:04.818 - Stats database initialized successfully
2025-05-29 10:36:03.264 - Using existing connection for thread 11872
2025-05-29 10:36:03.343 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:36:03.348 - New database connection created successfully for thread 5664
2025-05-29 10:36:04.202 - Database connection closed for thread 5664
2025-05-29 10:36:04.213 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:36:04.215 - New database connection created successfully for thread 5664
2025-05-29 10:36:04.224 - Database connection closed for thread 5664
2025-05-29 10:36:04.245 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:36:04.246 - New database connection created successfully for thread 5664
2025-05-29 10:36:04.629 - get_summary_stats called from thread 5664
2025-05-29 10:36:04.630 - Using existing connection for thread 5664
2025-05-29 10:36:04.631 - Total earnings from database: 382.0
2025-05-29 10:36:04.633 - Daily earnings from database: 0.0
2025-05-29 10:36:04.634 - Daily games from database: 0
2025-05-29 10:36:04.636 - Wallet balance from database: 0
2025-05-29 10:36:04.638 - Total games played from database: 8
2025-05-29 10:36:04.639 - Total winners from database: 8
2025-05-29 10:36:04.643 - Returning summary stats: {'total_earnings': 382.0, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-29 10:41:10.231 - Recreating expired connection for thread 5664
2025-05-29 10:41:10.246 - ensure_database_exists called from thread 11872
2025-05-29 10:41:10.247 - Recreating expired connection for thread 11872
2025-05-29 10:41:10.249 - Stats database initialized successfully
2025-05-29 10:41:13.582 - Database connection closed for thread 5664
2025-05-29 10:41:13.588 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.589 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.592 - Database connection closed for thread 5664
2025-05-29 10:41:13.593 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.595 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.599 - Database connection closed for thread 5664
2025-05-29 10:41:13.600 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.601 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.780 - Database connection closed for thread 5664
2025-05-29 10:41:13.784 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.786 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.798 - Database connection closed for thread 5664
2025-05-29 10:41:13.799 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.800 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.834 - Database connection closed for thread 5664
2025-05-29 10:41:13.835 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.837 - New database connection created successfully for thread 5664
2025-05-29 10:41:13.846 - Database connection closed for thread 5664
2025-05-29 10:41:13.847 - Creating new thread-specific database connection to data\stats.db for thread 5664
2025-05-29 10:41:13.848 - New database connection created successfully for thread 5664
2025-05-29 10:59:31.189 - ensure_database_exists called from thread 10628
2025-05-29 10:59:31.972 - Creating new thread-specific database connection to data\stats.db for thread 10628
2025-05-29 10:59:31.973 - New database connection created successfully for thread 10628
2025-05-29 10:59:31.976 - Stats database initialized successfully
2025-05-29 10:59:31.983 - ensure_database_exists called from thread 10628
2025-05-29 10:59:31.985 - Using existing connection for thread 10628
2025-05-29 10:59:31.985 - Stats database initialized successfully
2025-05-29 10:59:31.986 - ensure_database_exists called from thread 10628
2025-05-29 10:59:31.987 - Using existing connection for thread 10628
2025-05-29 10:59:31.989 - Stats database initialized successfully
2025-05-29 11:21:17.864 - ensure_database_exists called from thread 1080
2025-05-29 11:21:17.882 - Creating new thread-specific database connection to data\stats.db for thread 1080
2025-05-29 11:21:17.885 - New database connection created successfully for thread 1080
2025-05-29 11:21:17.887 - Stats database initialized successfully
2025-05-29 11:21:17.888 - ensure_database_exists called from thread 1080
2025-05-29 11:21:17.889 - Using existing connection for thread 1080
2025-05-29 11:21:17.890 - Stats database initialized successfully
2025-05-29 11:21:17.891 - ensure_database_exists called from thread 1080
2025-05-29 11:21:17.892 - Using existing connection for thread 1080
2025-05-29 11:21:17.893 - Stats database initialized successfully
2025-05-29 11:22:17.719 - Using existing connection for thread 1080
2025-05-29 11:22:17.813 - Creating new thread-specific database connection to data\stats.db for thread 11464
2025-05-29 11:22:17.815 - New database connection created successfully for thread 11464
2025-05-29 11:22:18.215 - Database connection closed for thread 11464
2025-05-29 11:22:18.216 - Creating new thread-specific database connection to data\stats.db for thread 11464
2025-05-29 11:22:18.218 - New database connection created successfully for thread 11464
2025-05-29 11:22:18.234 - Database connection closed for thread 11464
2025-05-29 11:22:18.235 - Creating new thread-specific database connection to data\stats.db for thread 11464
2025-05-29 11:22:18.236 - New database connection created successfully for thread 11464
2025-05-29 11:22:18.353 - get_summary_stats called from thread 11464
2025-05-29 11:22:18.354 - Using existing connection for thread 11464
2025-05-29 11:22:18.357 - Total earnings from database: 406.0
2025-05-29 11:22:18.367 - Daily earnings from database: 24.0
2025-05-29 11:22:18.370 - Daily games from database: 1
2025-05-29 11:22:18.374 - Wallet balance from database: 0
2025-05-29 11:22:18.380 - Total games played from database: 9
2025-05-29 11:22:18.385 - Total winners from database: 9
2025-05-29 11:22:18.394 - Returning summary stats: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 11:23:12.485 - ensure_database_exists called from thread 13716
2025-05-29 11:23:12.486 - Creating new thread-specific database connection to data\stats.db for thread 13716
2025-05-29 11:23:12.498 - New database connection created successfully for thread 13716
2025-05-29 11:23:12.501 - Stats database initialized successfully
2025-05-29 11:23:12.504 - ensure_database_exists called from thread 13716
2025-05-29 11:23:12.504 - Using existing connection for thread 13716
2025-05-29 11:23:12.505 - Stats database initialized successfully
2025-05-29 11:23:12.508 - ensure_database_exists called from thread 13716
2025-05-29 11:23:12.510 - Using existing connection for thread 13716
2025-05-29 11:23:12.511 - Stats database initialized successfully
2025-05-29 18:24:48.442 - ensure_database_exists called from thread 11540
2025-05-29 18:24:48.475 - Creating new thread-specific database connection to data\stats.db for thread 11540
2025-05-29 18:24:48.514 - New database connection created successfully for thread 11540
2025-05-29 18:24:48.663 - Stats database initialized successfully
2025-05-29 18:24:48.664 - ensure_database_exists called from thread 11540
2025-05-29 18:24:48.666 - Using existing connection for thread 11540
2025-05-29 18:24:48.667 - Stats database initialized successfully
2025-05-29 18:24:48.668 - ensure_database_exists called from thread 11540
2025-05-29 18:24:48.670 - Using existing connection for thread 11540
2025-05-29 18:24:48.671 - Stats database initialized successfully
2025-05-29 18:29:02.576 - Using existing connection for thread 11540
2025-05-29 18:29:02.592 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:29:02.597 - New database connection created successfully for thread 15552
2025-05-29 18:29:03.076 - Database connection closed for thread 15552
2025-05-29 18:29:03.079 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:29:03.083 - New database connection created successfully for thread 15552
2025-05-29 18:29:03.088 - Database connection closed for thread 15552
2025-05-29 18:29:03.090 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:29:03.092 - New database connection created successfully for thread 15552
2025-05-29 18:29:03.422 - get_summary_stats called from thread 15552
2025-05-29 18:29:03.424 - Using existing connection for thread 15552
2025-05-29 18:29:03.425 - Total earnings from database: 406.0
2025-05-29 18:29:03.428 - Daily earnings from database: 24.0
2025-05-29 18:29:03.429 - Daily games from database: 1
2025-05-29 18:29:03.430 - Wallet balance from database: 0
2025-05-29 18:29:03.432 - Total games played from database: 9
2025-05-29 18:29:03.437 - Total winners from database: 9
2025-05-29 18:29:03.449 - Returning summary stats: {'total_earnings': 406.0, 'daily_earnings': 24.0, 'daily_games': 1, 'wallet_balance': 0}
2025-05-29 18:33:25.017 - Using existing connection for thread 15552
2025-05-29 18:33:25.129 - Database connection closed for thread 15552
2025-05-29 18:33:25.143 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.144 - New database connection created successfully for thread 15552
2025-05-29 18:33:25.146 - Database connection closed for thread 15552
2025-05-29 18:33:25.146 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.148 - New database connection created successfully for thread 15552
2025-05-29 18:33:25.151 - Database connection closed for thread 15552
2025-05-29 18:33:25.153 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.154 - New database connection created successfully for thread 15552
2025-05-29 18:33:25.162 - Database connection closed for thread 15552
2025-05-29 18:33:25.163 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.165 - New database connection created successfully for thread 15552
2025-05-29 18:33:25.168 - Database connection closed for thread 15552
2025-05-29 18:33:25.169 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.170 - New database connection created successfully for thread 15552
2025-05-29 18:33:25.184 - ensure_database_exists called from thread 11540
2025-05-29 18:33:25.187 - Using existing connection for thread 11540
2025-05-29 18:33:25.188 - Stats database initialized successfully
2025-05-29 18:33:25.224 - Database connection closed for thread 15552
2025-05-29 18:33:25.228 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.231 - New database connection created successfully for thread 15552
2025-05-29 18:33:25.239 - Database connection closed for thread 15552
2025-05-29 18:33:25.241 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:33:25.242 - New database connection created successfully for thread 15552
2025-05-29 18:34:48.444 - Using existing connection for thread 11540
2025-05-29 18:34:48.529 - Using existing connection for thread 15552
2025-05-29 18:34:48.613 - Database connection closed for thread 15552
2025-05-29 18:34:48.618 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:34:48.622 - New database connection created successfully for thread 15552
2025-05-29 18:34:48.638 - Database connection closed for thread 15552
2025-05-29 18:34:48.644 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:34:48.651 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.097 - Creating new thread-specific database connection to data\stats.db for thread 12388
2025-05-29 18:37:19.099 - New database connection created successfully for thread 12388
2025-05-29 18:37:19.104 - Database connection closed for thread 12388
2025-05-29 18:37:19.109 - Creating new thread-specific database connection to data\stats.db for thread 12388
2025-05-29 18:37:19.111 - New database connection created successfully for thread 12388
2025-05-29 18:37:19.195 - Using existing connection for thread 15552
2025-05-29 18:37:19.345 - Database connection closed for thread 15552
2025-05-29 18:37:19.355 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.359 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.364 - Database connection closed for thread 15552
2025-05-29 18:37:19.367 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.371 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.381 - Database connection closed for thread 15552
2025-05-29 18:37:19.384 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.391 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.414 - Database connection closed for thread 15552
2025-05-29 18:37:19.418 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.422 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.428 - Database connection closed for thread 15552
2025-05-29 18:37:19.432 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.434 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.470 - Database connection closed for thread 15552
2025-05-29 18:37:19.474 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.479 - New database connection created successfully for thread 15552
2025-05-29 18:37:19.493 - Database connection closed for thread 15552
2025-05-29 18:37:19.499 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:37:19.509 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.865 - Using existing connection for thread 15552
2025-05-29 18:38:45.910 - Database connection closed for thread 15552
2025-05-29 18:38:45.923 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.925 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.926 - Database connection closed for thread 15552
2025-05-29 18:38:45.926 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.927 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.929 - Database connection closed for thread 15552
2025-05-29 18:38:45.930 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.931 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.945 - Database connection closed for thread 15552
2025-05-29 18:38:45.945 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.946 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.949 - Database connection closed for thread 15552
2025-05-29 18:38:45.950 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.951 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.969 - Database connection closed for thread 15552
2025-05-29 18:38:45.970 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.971 - New database connection created successfully for thread 15552
2025-05-29 18:38:45.974 - Database connection closed for thread 15552
2025-05-29 18:38:45.975 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:38:45.975 - New database connection created successfully for thread 15552
2025-05-29 18:40:21.368 - Recreating expired connection for thread 11540
2025-05-29 18:40:21.446 - Using existing connection for thread 15552
2025-05-29 18:40:22.015 - Database connection closed for thread 15552
2025-05-29 18:40:22.039 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:40:22.045 - New database connection created successfully for thread 15552
2025-05-29 18:40:22.075 - Database connection closed for thread 15552
2025-05-29 18:40:22.081 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:40:22.087 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.886 - Using existing connection for thread 15552
2025-05-29 18:43:22.924 - Database connection closed for thread 15552
2025-05-29 18:43:22.940 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.940 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.941 - Database connection closed for thread 15552
2025-05-29 18:43:22.942 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.943 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.945 - Database connection closed for thread 15552
2025-05-29 18:43:22.946 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.947 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.957 - Database connection closed for thread 15552
2025-05-29 18:43:22.962 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.962 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.966 - Database connection closed for thread 15552
2025-05-29 18:43:22.968 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.971 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.980 - Database connection closed for thread 15552
2025-05-29 18:43:22.984 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.986 - New database connection created successfully for thread 15552
2025-05-29 18:43:22.988 - Database connection closed for thread 15552
2025-05-29 18:43:22.990 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:43:22.993 - New database connection created successfully for thread 15552
2025-05-29 18:45:57.205 - Recreating expired connection for thread 11540
2025-05-29 18:45:57.210 - Using existing connection for thread 15552
2025-05-29 18:45:57.284 - Database connection closed for thread 15552
2025-05-29 18:45:57.290 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:45:57.297 - New database connection created successfully for thread 15552
2025-05-29 18:45:57.313 - Database connection closed for thread 15552
2025-05-29 18:45:57.318 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:45:57.326 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.227 - Using existing connection for thread 15552
2025-05-29 18:50:22.258 - Database connection closed for thread 15552
2025-05-29 18:50:22.299 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.300 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.301 - Database connection closed for thread 15552
2025-05-29 18:50:22.302 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.303 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.307 - Database connection closed for thread 15552
2025-05-29 18:50:22.307 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.308 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.321 - Database connection closed for thread 15552
2025-05-29 18:50:22.322 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.322 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.324 - Database connection closed for thread 15552
2025-05-29 18:50:22.326 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.327 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.337 - Database connection closed for thread 15552
2025-05-29 18:50:22.340 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.342 - New database connection created successfully for thread 15552
2025-05-29 18:50:22.344 - Database connection closed for thread 15552
2025-05-29 18:50:22.346 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:50:22.347 - New database connection created successfully for thread 15552
2025-05-29 18:51:15.823 - Recreating expired connection for thread 11540
2025-05-29 18:51:15.907 - Using existing connection for thread 15552
2025-05-29 18:51:15.994 - Database connection closed for thread 15552
2025-05-29 18:51:16.001 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:51:16.007 - New database connection created successfully for thread 15552
2025-05-29 18:51:16.027 - Database connection closed for thread 15552
2025-05-29 18:51:16.032 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:51:16.037 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.080 - Using existing connection for thread 15552
2025-05-29 18:55:21.129 - Database connection closed for thread 15552
2025-05-29 18:55:21.139 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.139 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.140 - Database connection closed for thread 15552
2025-05-29 18:55:21.141 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.141 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.144 - Database connection closed for thread 15552
2025-05-29 18:55:21.145 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.147 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.153 - Database connection closed for thread 15552
2025-05-29 18:55:21.154 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.155 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.160 - Database connection closed for thread 15552
2025-05-29 18:55:21.161 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.163 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.179 - Database connection closed for thread 15552
2025-05-29 18:55:21.180 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.181 - New database connection created successfully for thread 15552
2025-05-29 18:55:21.183 - Database connection closed for thread 15552
2025-05-29 18:55:21.184 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:21.184 - New database connection created successfully for thread 15552
2025-05-29 18:55:52.079 - Using existing connection for thread 11540
2025-05-29 18:55:52.097 - Using existing connection for thread 15552
2025-05-29 18:55:52.416 - Database connection closed for thread 15552
2025-05-29 18:55:52.424 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:52.432 - New database connection created successfully for thread 15552
2025-05-29 18:55:52.448 - Database connection closed for thread 15552
2025-05-29 18:55:52.453 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 18:55:52.461 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.798 - Using existing connection for thread 15552
2025-05-29 19:00:25.830 - Database connection closed for thread 15552
2025-05-29 19:00:25.847 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.847 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.848 - Database connection closed for thread 15552
2025-05-29 19:00:25.849 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.850 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.852 - Database connection closed for thread 15552
2025-05-29 19:00:25.853 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.855 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.864 - Database connection closed for thread 15552
2025-05-29 19:00:25.866 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.867 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.871 - Database connection closed for thread 15552
2025-05-29 19:00:25.872 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.873 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.887 - Database connection closed for thread 15552
2025-05-29 19:00:25.889 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.890 - New database connection created successfully for thread 15552
2025-05-29 19:00:25.893 - Database connection closed for thread 15552
2025-05-29 19:00:25.894 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:00:25.894 - New database connection created successfully for thread 15552
2025-05-29 19:03:13.946 - Recreating expired connection for thread 11540
2025-05-29 19:03:13.999 - Using existing connection for thread 15552
2025-05-29 19:03:14.067 - Database connection closed for thread 15552
2025-05-29 19:03:14.072 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:03:14.073 - New database connection created successfully for thread 15552
2025-05-29 19:03:14.080 - Database connection closed for thread 15552
2025-05-29 19:03:14.082 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:03:14.083 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.282 - Using existing connection for thread 15552
2025-05-29 19:05:50.324 - Database connection closed for thread 15552
2025-05-29 19:05:50.344 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.345 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.346 - Database connection closed for thread 15552
2025-05-29 19:05:50.347 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.348 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.350 - Database connection closed for thread 15552
2025-05-29 19:05:50.351 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.357 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.364 - Database connection closed for thread 15552
2025-05-29 19:05:50.365 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.366 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.371 - Database connection closed for thread 15552
2025-05-29 19:05:50.372 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.373 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.392 - Database connection closed for thread 15552
2025-05-29 19:05:50.393 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.393 - New database connection created successfully for thread 15552
2025-05-29 19:05:50.397 - Database connection closed for thread 15552
2025-05-29 19:05:50.398 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:05:50.399 - New database connection created successfully for thread 15552
2025-05-29 19:09:38.182 - Recreating expired connection for thread 11540
2025-05-29 19:09:38.202 - Using existing connection for thread 15552
2025-05-29 19:09:38.262 - Database connection closed for thread 15552
2025-05-29 19:09:38.275 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:09:38.279 - New database connection created successfully for thread 15552
2025-05-29 19:09:38.295 - Database connection closed for thread 15552
2025-05-29 19:09:38.301 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:09:38.304 - New database connection created successfully for thread 15552
2025-05-29 19:11:58.870 - Using existing connection for thread 15552
2025-05-29 19:11:59.036 - Database connection closed for thread 15552
2025-05-29 19:11:59.058 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.058 - New database connection created successfully for thread 15552
2025-05-29 19:11:59.060 - Database connection closed for thread 15552
2025-05-29 19:11:59.060 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.061 - New database connection created successfully for thread 15552
2025-05-29 19:11:59.066 - Database connection closed for thread 15552
2025-05-29 19:11:59.068 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.071 - New database connection created successfully for thread 15552
2025-05-29 19:11:59.077 - Database connection closed for thread 15552
2025-05-29 19:11:59.078 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.081 - New database connection created successfully for thread 15552
2025-05-29 19:11:59.087 - Database connection closed for thread 15552
2025-05-29 19:11:59.088 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.091 - New database connection created successfully for thread 15552
2025-05-29 19:11:59.106 - Database connection closed for thread 15552
2025-05-29 19:11:59.107 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.108 - New database connection created successfully for thread 15552
2025-05-29 19:11:59.111 - Database connection closed for thread 15552
2025-05-29 19:11:59.112 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:11:59.112 - New database connection created successfully for thread 15552
2025-05-29 19:13:40.142 - Using existing connection for thread 11540
2025-05-29 19:13:40.186 - Using existing connection for thread 15552
2025-05-29 19:13:40.230 - Database connection closed for thread 15552
2025-05-29 19:13:40.231 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:13:40.232 - New database connection created successfully for thread 15552
2025-05-29 19:13:40.242 - Database connection closed for thread 15552
2025-05-29 19:13:40.243 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:13:40.245 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.058 - Using existing connection for thread 15552
2025-05-29 19:15:42.160 - Database connection closed for thread 15552
2025-05-29 19:15:42.178 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.179 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.181 - Database connection closed for thread 15552
2025-05-29 19:15:42.182 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.183 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.186 - Database connection closed for thread 15552
2025-05-29 19:15:42.187 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.188 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.198 - Database connection closed for thread 15552
2025-05-29 19:15:42.200 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.203 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.205 - Database connection closed for thread 15552
2025-05-29 19:15:42.206 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.207 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.229 - Database connection closed for thread 15552
2025-05-29 19:15:42.230 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.231 - New database connection created successfully for thread 15552
2025-05-29 19:15:42.235 - Database connection closed for thread 15552
2025-05-29 19:15:42.236 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:15:42.237 - New database connection created successfully for thread 15552
2025-05-29 19:18:02.113 - Using existing connection for thread 11540
2025-05-29 19:18:02.168 - Using existing connection for thread 15552
2025-05-29 19:18:02.640 - Database connection closed for thread 15552
2025-05-29 19:18:02.642 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:02.644 - New database connection created successfully for thread 15552
2025-05-29 19:18:02.682 - Database connection closed for thread 15552
2025-05-29 19:18:02.692 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:02.699 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.148 - Using existing connection for thread 15552
2025-05-29 19:18:09.299 - Database connection closed for thread 15552
2025-05-29 19:18:09.305 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.306 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.307 - Database connection closed for thread 15552
2025-05-29 19:18:09.308 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.308 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.311 - Database connection closed for thread 15552
2025-05-29 19:18:09.312 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.314 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.324 - Database connection closed for thread 15552
2025-05-29 19:18:09.325 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.328 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.331 - Database connection closed for thread 15552
2025-05-29 19:18:09.332 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.333 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.348 - Database connection closed for thread 15552
2025-05-29 19:18:09.350 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.397 - New database connection created successfully for thread 15552
2025-05-29 19:18:09.409 - Database connection closed for thread 15552
2025-05-29 19:18:09.410 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:18:09.412 - New database connection created successfully for thread 15552
2025-05-29 19:19:18.699 - Using existing connection for thread 11540
2025-05-29 19:19:18.764 - Using existing connection for thread 15552
2025-05-29 19:19:19.342 - Database connection closed for thread 15552
2025-05-29 19:19:19.343 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:19:19.344 - New database connection created successfully for thread 15552
2025-05-29 19:19:19.347 - Database connection closed for thread 15552
2025-05-29 19:19:19.348 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:19:19.348 - New database connection created successfully for thread 15552
2025-05-29 19:21:28.820 - Using existing connection for thread 15552
2025-05-29 19:21:29.037 - Database connection closed for thread 15552
2025-05-29 19:21:29.065 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.066 - New database connection created successfully for thread 15552
2025-05-29 19:21:29.067 - Database connection closed for thread 15552
2025-05-29 19:21:29.068 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.069 - New database connection created successfully for thread 15552
2025-05-29 19:21:29.073 - Database connection closed for thread 15552
2025-05-29 19:21:29.074 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.078 - New database connection created successfully for thread 15552
2025-05-29 19:21:29.091 - Database connection closed for thread 15552
2025-05-29 19:21:29.092 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.093 - New database connection created successfully for thread 15552
2025-05-29 19:21:29.110 - Database connection closed for thread 15552
2025-05-29 19:21:29.111 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.113 - New database connection created successfully for thread 15552
2025-05-29 19:21:29.211 - Database connection closed for thread 15552
2025-05-29 19:21:29.238 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.254 - New database connection created successfully for thread 15552
2025-05-29 19:21:29.288 - Database connection closed for thread 15552
2025-05-29 19:21:29.296 - Creating new thread-specific database connection to data\stats.db for thread 15552
2025-05-29 19:21:29.308 - New database connection created successfully for thread 15552
2025-05-30 18:39:52.548 - ensure_database_exists called from thread 16892
2025-05-30 18:39:52.572 - Creating new thread-specific database connection to data\stats.db for thread 16892
2025-05-30 18:39:52.667 - New database connection created successfully for thread 16892
2025-05-30 18:39:52.715 - Stats database initialized successfully
2025-05-30 18:39:52.716 - ensure_database_exists called from thread 16892
2025-05-30 18:39:52.719 - Using existing connection for thread 16892
2025-05-30 18:39:52.726 - Stats database initialized successfully
2025-05-30 18:39:52.727 - ensure_database_exists called from thread 16892
2025-05-30 18:39:52.728 - Using existing connection for thread 16892
2025-05-30 18:39:52.731 - Stats database initialized successfully
2025-05-30 18:47:30.394 - Recreating expired connection for thread 16892
2025-05-30 18:47:30.394 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:47:30.396 - New database connection created successfully for thread 5256
2025-05-30 18:47:30.487 - Database connection closed for thread 5256
2025-05-30 18:47:30.489 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:47:30.491 - New database connection created successfully for thread 5256
2025-05-30 18:47:30.518 - Database connection closed for thread 5256
2025-05-30 18:47:30.524 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:47:30.538 - New database connection created successfully for thread 5256
2025-05-30 18:47:30.783 - get_summary_stats called from thread 5256
2025-05-30 18:47:30.783 - Using existing connection for thread 5256
2025-05-30 18:47:30.784 - Total earnings from database: 692.4
2025-05-30 18:47:30.785 - Daily earnings from database: 0.0
2025-05-30 18:47:30.785 - Daily games from database: 0
2025-05-30 18:47:30.786 - Wallet balance from database: 0
2025-05-30 18:47:30.786 - Total games played from database: 21
2025-05-30 18:47:30.808 - Total winners from database: 21
2025-05-30 18:47:30.808 - Returning summary stats: {'total_earnings': 692.4, 'daily_earnings': 0.0, 'daily_games': 0, 'wallet_balance': 0}
2025-05-30 18:50:13.317 - Using existing connection for thread 5256
2025-05-30 18:50:13.329 - ensure_database_exists called from thread 16892
2025-05-30 18:50:13.329 - Using existing connection for thread 16892
2025-05-30 18:50:13.330 - Stats database initialized successfully
2025-05-30 18:50:13.397 - Database connection closed for thread 5256
2025-05-30 18:50:13.422 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.424 - New database connection created successfully for thread 5256
2025-05-30 18:50:13.426 - Database connection closed for thread 5256
2025-05-30 18:50:13.427 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.429 - New database connection created successfully for thread 5256
2025-05-30 18:50:13.434 - Database connection closed for thread 5256
2025-05-30 18:50:13.435 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.436 - New database connection created successfully for thread 5256
2025-05-30 18:50:13.443 - Database connection closed for thread 5256
2025-05-30 18:50:13.446 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.447 - New database connection created successfully for thread 5256
2025-05-30 18:50:13.451 - Database connection closed for thread 5256
2025-05-30 18:50:13.451 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.452 - New database connection created successfully for thread 5256
2025-05-30 18:50:13.477 - Database connection closed for thread 5256
2025-05-30 18:50:13.478 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.479 - New database connection created successfully for thread 5256
2025-05-30 18:50:13.484 - Database connection closed for thread 5256
2025-05-30 18:50:13.485 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:50:13.485 - New database connection created successfully for thread 5256
2025-05-30 18:52:20.856 - Using existing connection for thread 16892
2025-05-30 18:52:20.878 - Using existing connection for thread 5256
2025-05-30 18:52:20.994 - Database connection closed for thread 5256
2025-05-30 18:52:20.995 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:52:20.999 - New database connection created successfully for thread 5256
2025-05-30 18:52:21.025 - Database connection closed for thread 5256
2025-05-30 18:52:21.026 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:52:21.028 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.690 - Using existing connection for thread 5256
2025-05-30 18:55:06.746 - Database connection closed for thread 5256
2025-05-30 18:55:06.759 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.760 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.762 - Database connection closed for thread 5256
2025-05-30 18:55:06.763 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.764 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.768 - Database connection closed for thread 5256
2025-05-30 18:55:06.769 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.770 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.779 - Database connection closed for thread 5256
2025-05-30 18:55:06.781 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.783 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.788 - Database connection closed for thread 5256
2025-05-30 18:55:06.789 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.789 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.802 - Database connection closed for thread 5256
2025-05-30 18:55:06.803 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.803 - New database connection created successfully for thread 5256
2025-05-30 18:55:06.808 - Database connection closed for thread 5256
2025-05-30 18:55:06.808 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:55:06.809 - New database connection created successfully for thread 5256
2025-05-30 18:56:39.926 - Using existing connection for thread 16892
2025-05-30 18:56:39.981 - Using existing connection for thread 5256
2025-05-30 18:56:40.087 - Database connection closed for thread 5256
2025-05-30 18:56:40.089 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:56:40.089 - New database connection created successfully for thread 5256
2025-05-30 18:56:40.102 - Database connection closed for thread 5256
2025-05-30 18:56:40.103 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:56:40.103 - New database connection created successfully for thread 5256
2025-05-30 18:59:27.783 - Using existing connection for thread 5256
2025-05-30 18:59:27.850 - Database connection closed for thread 5256
2025-05-30 18:59:27.903 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:27.905 - New database connection created successfully for thread 5256
2025-05-30 18:59:27.910 - Database connection closed for thread 5256
2025-05-30 18:59:27.912 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:27.914 - New database connection created successfully for thread 5256
2025-05-30 18:59:27.926 - Database connection closed for thread 5256
2025-05-30 18:59:27.930 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:27.932 - New database connection created successfully for thread 5256
2025-05-30 18:59:27.948 - Database connection closed for thread 5256
2025-05-30 18:59:27.951 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:27.955 - New database connection created successfully for thread 5256
2025-05-30 18:59:27.974 - Database connection closed for thread 5256
2025-05-30 18:59:27.977 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:27.980 - New database connection created successfully for thread 5256
2025-05-30 18:59:28.020 - Database connection closed for thread 5256
2025-05-30 18:59:28.022 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:28.024 - New database connection created successfully for thread 5256
2025-05-30 18:59:28.035 - Database connection closed for thread 5256
2025-05-30 18:59:28.039 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 18:59:28.040 - New database connection created successfully for thread 5256
2025-05-30 19:02:07.013 - Recreating expired connection for thread 16892
2025-05-30 19:02:07.027 - Using existing connection for thread 5256
2025-05-30 19:02:07.149 - Database connection closed for thread 5256
2025-05-30 19:02:07.150 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:02:07.150 - New database connection created successfully for thread 5256
2025-05-30 19:02:07.163 - Database connection closed for thread 5256
2025-05-30 19:02:07.164 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:02:07.165 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.492 - Using existing connection for thread 5256
2025-05-30 19:04:42.559 - Database connection closed for thread 5256
2025-05-30 19:04:42.572 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.573 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.574 - Database connection closed for thread 5256
2025-05-30 19:04:42.575 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.576 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.579 - Database connection closed for thread 5256
2025-05-30 19:04:42.579 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.580 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.586 - Database connection closed for thread 5256
2025-05-30 19:04:42.587 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.589 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.595 - Database connection closed for thread 5256
2025-05-30 19:04:42.596 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.597 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.608 - Database connection closed for thread 5256
2025-05-30 19:04:42.609 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.610 - New database connection created successfully for thread 5256
2025-05-30 19:04:42.613 - Database connection closed for thread 5256
2025-05-30 19:04:42.614 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:04:42.615 - New database connection created successfully for thread 5256
2025-05-30 19:09:13.132 - Recreating expired connection for thread 16892
2025-05-30 19:09:13.212 - Using existing connection for thread 5256
2025-05-30 19:09:13.300 - Database connection closed for thread 5256
2025-05-30 19:09:13.300 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:13.301 - New database connection created successfully for thread 5256
2025-05-30 19:09:13.314 - Database connection closed for thread 5256
2025-05-30 19:09:13.314 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:13.316 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.472 - Using existing connection for thread 5256
2025-05-30 19:09:16.516 - Database connection closed for thread 5256
2025-05-30 19:09:16.529 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.530 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.532 - Database connection closed for thread 5256
2025-05-30 19:09:16.532 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.533 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.536 - Database connection closed for thread 5256
2025-05-30 19:09:16.537 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.538 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.543 - Database connection closed for thread 5256
2025-05-30 19:09:16.544 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.545 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.550 - Database connection closed for thread 5256
2025-05-30 19:09:16.551 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.553 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.565 - Database connection closed for thread 5256
2025-05-30 19:09:16.565 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.566 - New database connection created successfully for thread 5256
2025-05-30 19:09:16.569 - Database connection closed for thread 5256
2025-05-30 19:09:16.570 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:16.573 - New database connection created successfully for thread 5256
2025-05-30 19:09:35.772 - Using existing connection for thread 16892
2025-05-30 19:09:35.828 - Using existing connection for thread 5256
2025-05-30 19:09:35.925 - Database connection closed for thread 5256
2025-05-30 19:09:35.926 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:35.926 - New database connection created successfully for thread 5256
2025-05-30 19:09:35.949 - Database connection closed for thread 5256
2025-05-30 19:09:35.950 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:09:35.952 - New database connection created successfully for thread 5256
2025-05-30 19:13:21.949 - Using existing connection for thread 5256
2025-05-30 19:13:22.002 - Database connection closed for thread 5256
2025-05-30 19:13:22.015 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.016 - New database connection created successfully for thread 5256
2025-05-30 19:13:22.017 - Database connection closed for thread 5256
2025-05-30 19:13:22.018 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.018 - New database connection created successfully for thread 5256
2025-05-30 19:13:22.021 - Database connection closed for thread 5256
2025-05-30 19:13:22.022 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.023 - New database connection created successfully for thread 5256
2025-05-30 19:13:22.029 - Database connection closed for thread 5256
2025-05-30 19:13:22.030 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.031 - New database connection created successfully for thread 5256
2025-05-30 19:13:22.039 - Database connection closed for thread 5256
2025-05-30 19:13:22.040 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.040 - New database connection created successfully for thread 5256
2025-05-30 19:13:22.051 - Database connection closed for thread 5256
2025-05-30 19:13:22.052 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.053 - New database connection created successfully for thread 5256
2025-05-30 19:13:22.057 - Database connection closed for thread 5256
2025-05-30 19:13:22.057 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:13:22.058 - New database connection created successfully for thread 5256
2025-05-30 19:15:35.689 - Recreating expired connection for thread 16892
2025-05-30 19:15:35.768 - Using existing connection for thread 5256
2025-05-30 19:15:35.872 - Database connection closed for thread 5256
2025-05-30 19:15:35.874 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:15:35.874 - New database connection created successfully for thread 5256
2025-05-30 19:15:35.892 - Database connection closed for thread 5256
2025-05-30 19:15:35.898 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:15:35.901 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.565 - Using existing connection for thread 5256
2025-05-30 19:17:21.601 - Database connection closed for thread 5256
2025-05-30 19:17:21.615 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.615 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.616 - Database connection closed for thread 5256
2025-05-30 19:17:21.617 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.618 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.621 - Database connection closed for thread 5256
2025-05-30 19:17:21.622 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.622 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.628 - Database connection closed for thread 5256
2025-05-30 19:17:21.629 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.630 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.635 - Database connection closed for thread 5256
2025-05-30 19:17:21.635 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.636 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.648 - Database connection closed for thread 5256
2025-05-30 19:17:21.650 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.651 - New database connection created successfully for thread 5256
2025-05-30 19:17:21.654 - Database connection closed for thread 5256
2025-05-30 19:17:21.655 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:17:21.656 - New database connection created successfully for thread 5256
2025-05-30 19:19:07.259 - Using existing connection for thread 16892
2025-05-30 19:19:07.318 - Using existing connection for thread 5256
2025-05-30 19:19:07.396 - Database connection closed for thread 5256
2025-05-30 19:19:07.397 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:19:07.399 - New database connection created successfully for thread 5256
2025-05-30 19:19:07.418 - Database connection closed for thread 5256
2025-05-30 19:19:07.420 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:19:07.421 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.723 - Using existing connection for thread 5256
2025-05-30 19:23:42.765 - Database connection closed for thread 5256
2025-05-30 19:23:42.789 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.790 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.791 - Database connection closed for thread 5256
2025-05-30 19:23:42.792 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.792 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.796 - Database connection closed for thread 5256
2025-05-30 19:23:42.797 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.797 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.804 - Database connection closed for thread 5256
2025-05-30 19:23:42.804 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.806 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.814 - Database connection closed for thread 5256
2025-05-30 19:23:42.814 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.815 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.827 - Database connection closed for thread 5256
2025-05-30 19:23:42.827 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.828 - New database connection created successfully for thread 5256
2025-05-30 19:23:42.831 - Database connection closed for thread 5256
2025-05-30 19:23:42.832 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:23:42.833 - New database connection created successfully for thread 5256
2025-05-30 19:24:35.044 - Recreating expired connection for thread 16892
2025-05-30 19:24:35.121 - Using existing connection for thread 5256
2025-05-30 19:24:35.240 - Database connection closed for thread 5256
2025-05-30 19:24:35.243 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:24:35.244 - New database connection created successfully for thread 5256
2025-05-30 19:24:35.270 - Database connection closed for thread 5256
2025-05-30 19:24:35.272 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:24:35.273 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.798 - Recreating expired connection for thread 5256
2025-05-30 19:30:17.835 - Database connection closed for thread 5256
2025-05-30 19:30:17.849 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.850 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.851 - Database connection closed for thread 5256
2025-05-30 19:30:17.852 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.852 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.855 - Database connection closed for thread 5256
2025-05-30 19:30:17.856 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.857 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.862 - Database connection closed for thread 5256
2025-05-30 19:30:17.863 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.865 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.870 - Database connection closed for thread 5256
2025-05-30 19:30:17.870 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.871 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.882 - Database connection closed for thread 5256
2025-05-30 19:30:17.883 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.883 - New database connection created successfully for thread 5256
2025-05-30 19:30:17.899 - Database connection closed for thread 5256
2025-05-30 19:30:17.900 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:17.901 - New database connection created successfully for thread 5256
2025-05-30 19:30:52.119 - Recreating expired connection for thread 16892
2025-05-30 19:30:52.137 - Using existing connection for thread 5256
2025-05-30 19:30:52.248 - Database connection closed for thread 5256
2025-05-30 19:30:52.249 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:52.251 - New database connection created successfully for thread 5256
2025-05-30 19:30:52.273 - Database connection closed for thread 5256
2025-05-30 19:30:52.276 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:30:52.279 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.058 - Using existing connection for thread 5256
2025-05-30 19:34:18.113 - Database connection closed for thread 5256
2025-05-30 19:34:18.127 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.127 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.129 - Database connection closed for thread 5256
2025-05-30 19:34:18.129 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.130 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.133 - Database connection closed for thread 5256
2025-05-30 19:34:18.134 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.136 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.140 - Database connection closed for thread 5256
2025-05-30 19:34:18.142 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.143 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.158 - Database connection closed for thread 5256
2025-05-30 19:34:18.160 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.161 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.173 - Database connection closed for thread 5256
2025-05-30 19:34:18.174 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.175 - New database connection created successfully for thread 5256
2025-05-30 19:34:18.178 - Database connection closed for thread 5256
2025-05-30 19:34:18.179 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:34:18.179 - New database connection created successfully for thread 5256
2025-05-30 19:36:43.796 - Recreating expired connection for thread 16892
2025-05-30 19:36:43.855 - Using existing connection for thread 5256
2025-05-30 19:36:43.949 - Database connection closed for thread 5256
2025-05-30 19:36:43.951 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:36:43.952 - New database connection created successfully for thread 5256
2025-05-30 19:36:43.969 - Database connection closed for thread 5256
2025-05-30 19:36:43.971 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:36:43.972 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.321 - Recreating expired connection for thread 5256
2025-05-30 19:42:05.369 - Database connection closed for thread 5256
2025-05-30 19:42:05.404 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.407 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.410 - Database connection closed for thread 5256
2025-05-30 19:42:05.414 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.416 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.458 - Database connection closed for thread 5256
2025-05-30 19:42:05.462 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.464 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.481 - Database connection closed for thread 5256
2025-05-30 19:42:05.483 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.487 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.513 - Database connection closed for thread 5256
2025-05-30 19:42:05.515 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.520 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.572 - Database connection closed for thread 5256
2025-05-30 19:42:05.574 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.575 - New database connection created successfully for thread 5256
2025-05-30 19:42:05.579 - Database connection closed for thread 5256
2025-05-30 19:42:05.581 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:42:05.581 - New database connection created successfully for thread 5256
2025-05-30 19:45:24.573 - Recreating expired connection for thread 16892
2025-05-30 19:45:24.589 - Using existing connection for thread 5256
2025-05-30 19:45:24.651 - Database connection closed for thread 5256
2025-05-30 19:45:24.659 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:45:24.662 - New database connection created successfully for thread 5256
2025-05-30 19:45:24.682 - Database connection closed for thread 5256
2025-05-30 19:45:24.686 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:45:24.687 - New database connection created successfully for thread 5256
2025-05-30 19:47:52.998 - Using existing connection for thread 5256
2025-05-30 19:47:53.152 - Database connection closed for thread 5256
2025-05-30 19:47:53.166 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.166 - New database connection created successfully for thread 5256
2025-05-30 19:47:53.168 - Database connection closed for thread 5256
2025-05-30 19:47:53.169 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.169 - New database connection created successfully for thread 5256
2025-05-30 19:47:53.174 - Database connection closed for thread 5256
2025-05-30 19:47:53.175 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.175 - New database connection created successfully for thread 5256
2025-05-30 19:47:53.180 - Database connection closed for thread 5256
2025-05-30 19:47:53.182 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.184 - New database connection created successfully for thread 5256
2025-05-30 19:47:53.188 - Database connection closed for thread 5256
2025-05-30 19:47:53.188 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.189 - New database connection created successfully for thread 5256
2025-05-30 19:47:53.201 - Database connection closed for thread 5256
2025-05-30 19:47:53.202 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.204 - New database connection created successfully for thread 5256
2025-05-30 19:47:53.207 - Database connection closed for thread 5256
2025-05-30 19:47:53.208 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:47:53.209 - New database connection created successfully for thread 5256
2025-05-30 19:51:33.097 - Recreating expired connection for thread 16892
2025-05-30 19:51:33.135 - Using existing connection for thread 5256
2025-05-30 19:51:33.239 - Database connection closed for thread 5256
2025-05-30 19:51:33.239 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:51:33.240 - New database connection created successfully for thread 5256
2025-05-30 19:51:33.254 - Database connection closed for thread 5256
2025-05-30 19:51:33.255 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:51:33.256 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.001 - Recreating expired connection for thread 5256
2025-05-30 19:56:34.147 - Database connection closed for thread 5256
2025-05-30 19:56:34.160 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.161 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.162 - Database connection closed for thread 5256
2025-05-30 19:56:34.162 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.163 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.167 - Database connection closed for thread 5256
2025-05-30 19:56:34.168 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.168 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.175 - Database connection closed for thread 5256
2025-05-30 19:56:34.177 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.178 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.183 - Database connection closed for thread 5256
2025-05-30 19:56:34.185 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.185 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.197 - Database connection closed for thread 5256
2025-05-30 19:56:34.198 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.198 - New database connection created successfully for thread 5256
2025-05-30 19:56:34.201 - Database connection closed for thread 5256
2025-05-30 19:56:34.202 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:56:34.202 - New database connection created successfully for thread 5256
2025-05-30 19:58:19.701 - Recreating expired connection for thread 16892
2025-05-30 19:58:19.715 - Using existing connection for thread 5256
2025-05-30 19:58:19.822 - Database connection closed for thread 5256
2025-05-30 19:58:19.824 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:58:19.826 - New database connection created successfully for thread 5256
2025-05-30 19:58:19.855 - Database connection closed for thread 5256
2025-05-30 19:58:19.859 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 19:58:19.860 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.836 - Using existing connection for thread 5256
2025-05-30 20:00:03.877 - Database connection closed for thread 5256
2025-05-30 20:00:03.892 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.892 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.893 - Database connection closed for thread 5256
2025-05-30 20:00:03.894 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.895 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.899 - Database connection closed for thread 5256
2025-05-30 20:00:03.900 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.900 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.905 - Database connection closed for thread 5256
2025-05-30 20:00:03.906 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.908 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.912 - Database connection closed for thread 5256
2025-05-30 20:00:03.913 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.914 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.925 - Database connection closed for thread 5256
2025-05-30 20:00:03.926 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.926 - New database connection created successfully for thread 5256
2025-05-30 20:00:03.930 - Database connection closed for thread 5256
2025-05-30 20:00:03.931 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:00:03.932 - New database connection created successfully for thread 5256
2025-05-30 20:04:07.185 - Recreating expired connection for thread 16892
2025-05-30 20:04:07.238 - Using existing connection for thread 5256
2025-05-30 20:04:07.341 - Database connection closed for thread 5256
2025-05-30 20:04:07.342 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:04:07.342 - New database connection created successfully for thread 5256
2025-05-30 20:04:07.356 - Database connection closed for thread 5256
2025-05-30 20:04:07.357 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:04:07.358 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.159 - Recreating expired connection for thread 5256
2025-05-30 20:10:55.213 - Database connection closed for thread 5256
2025-05-30 20:10:55.224 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.224 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.226 - Database connection closed for thread 5256
2025-05-30 20:10:55.227 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.227 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.230 - Database connection closed for thread 5256
2025-05-30 20:10:55.231 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.231 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.236 - Database connection closed for thread 5256
2025-05-30 20:10:55.237 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.239 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.243 - Database connection closed for thread 5256
2025-05-30 20:10:55.244 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.245 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.256 - Database connection closed for thread 5256
2025-05-30 20:10:55.257 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.257 - New database connection created successfully for thread 5256
2025-05-30 20:10:55.260 - Database connection closed for thread 5256
2025-05-30 20:10:55.261 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:10:55.262 - New database connection created successfully for thread 5256
2025-05-30 20:12:04.643 - Recreating expired connection for thread 16892
2025-05-30 20:12:04.741 - Using existing connection for thread 5256
2025-05-30 20:12:04.815 - Database connection closed for thread 5256
2025-05-30 20:12:04.816 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:04.817 - New database connection created successfully for thread 5256
2025-05-30 20:12:04.834 - Database connection closed for thread 5256
2025-05-30 20:12:04.837 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:04.838 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.327 - Using existing connection for thread 5256
2025-05-30 20:12:11.395 - Database connection closed for thread 5256
2025-05-30 20:12:11.406 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.407 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.409 - Database connection closed for thread 5256
2025-05-30 20:12:11.409 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.410 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.413 - Database connection closed for thread 5256
2025-05-30 20:12:11.414 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.414 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.419 - Database connection closed for thread 5256
2025-05-30 20:12:11.421 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.423 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.426 - Database connection closed for thread 5256
2025-05-30 20:12:11.427 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.428 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.439 - Database connection closed for thread 5256
2025-05-30 20:12:11.439 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.440 - New database connection created successfully for thread 5256
2025-05-30 20:12:11.443 - Database connection closed for thread 5256
2025-05-30 20:12:11.444 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:11.445 - New database connection created successfully for thread 5256
2025-05-30 20:12:40.901 - Using existing connection for thread 16892
2025-05-30 20:12:40.924 - Using existing connection for thread 5256
2025-05-30 20:12:41.031 - Database connection closed for thread 5256
2025-05-30 20:12:41.032 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:41.032 - New database connection created successfully for thread 5256
2025-05-30 20:12:41.048 - Database connection closed for thread 5256
2025-05-30 20:12:41.049 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:12:41.050 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.877 - Recreating expired connection for thread 5256
2025-05-30 20:17:57.926 - Database connection closed for thread 5256
2025-05-30 20:17:57.938 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.940 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.941 - Database connection closed for thread 5256
2025-05-30 20:17:57.941 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.942 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.945 - Database connection closed for thread 5256
2025-05-30 20:17:57.946 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.947 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.952 - Database connection closed for thread 5256
2025-05-30 20:17:57.953 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.955 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.958 - Database connection closed for thread 5256
2025-05-30 20:17:57.960 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.960 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.971 - Database connection closed for thread 5256
2025-05-30 20:17:57.972 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.972 - New database connection created successfully for thread 5256
2025-05-30 20:17:57.975 - Database connection closed for thread 5256
2025-05-30 20:17:57.977 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:17:57.977 - New database connection created successfully for thread 5256
2025-05-30 20:20:26.366 - Recreating expired connection for thread 16892
2025-05-30 20:20:26.390 - Using existing connection for thread 5256
2025-05-30 20:20:26.551 - Database connection closed for thread 5256
2025-05-30 20:20:26.552 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:20:26.554 - New database connection created successfully for thread 5256
2025-05-30 20:20:26.573 - Database connection closed for thread 5256
2025-05-30 20:20:26.574 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:20:26.575 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.910 - Using existing connection for thread 5256
2025-05-30 20:24:39.950 - Database connection closed for thread 5256
2025-05-30 20:24:39.962 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.962 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.963 - Database connection closed for thread 5256
2025-05-30 20:24:39.964 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.964 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.967 - Database connection closed for thread 5256
2025-05-30 20:24:39.968 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.969 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.974 - Database connection closed for thread 5256
2025-05-30 20:24:39.975 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.976 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.980 - Database connection closed for thread 5256
2025-05-30 20:24:39.981 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.982 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.993 - Database connection closed for thread 5256
2025-05-30 20:24:39.993 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.994 - New database connection created successfully for thread 5256
2025-05-30 20:24:39.997 - Database connection closed for thread 5256
2025-05-30 20:24:39.998 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:24:39.998 - New database connection created successfully for thread 5256
2025-05-30 20:25:32.456 - Recreating expired connection for thread 16892
2025-05-30 20:25:32.493 - Using existing connection for thread 5256
2025-05-30 20:25:32.559 - Database connection closed for thread 5256
2025-05-30 20:25:32.559 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:25:32.560 - New database connection created successfully for thread 5256
2025-05-30 20:25:32.575 - Database connection closed for thread 5256
2025-05-30 20:25:32.576 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:25:32.576 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.393 - Using existing connection for thread 5256
2025-05-30 20:29:18.425 - Database connection closed for thread 5256
2025-05-30 20:29:18.437 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.437 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.438 - Database connection closed for thread 5256
2025-05-30 20:29:18.439 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.439 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.442 - Database connection closed for thread 5256
2025-05-30 20:29:18.443 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.443 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.451 - Database connection closed for thread 5256
2025-05-30 20:29:18.452 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.453 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.459 - Database connection closed for thread 5256
2025-05-30 20:29:18.460 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.460 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.470 - Database connection closed for thread 5256
2025-05-30 20:29:18.470 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.472 - New database connection created successfully for thread 5256
2025-05-30 20:29:18.475 - Database connection closed for thread 5256
2025-05-30 20:29:18.475 - Creating new thread-specific database connection to data\stats.db for thread 5256
2025-05-30 20:29:18.476 - New database connection created successfully for thread 5256
2025-05-30 22:35:12.473 - ensure_database_exists called from thread 3920
2025-05-30 22:35:12.477 - Creating new thread-specific database connection to data\stats.db for thread 3920
2025-05-30 22:35:12.502 - New database connection created successfully for thread 3920
2025-05-30 22:35:12.780 - Stats database initialized successfully
2025-05-30 22:35:12.781 - ensure_database_exists called from thread 3920
2025-05-30 22:35:12.782 - Using existing connection for thread 3920
2025-05-30 22:35:12.783 - Stats database initialized successfully
2025-05-30 22:35:12.785 - ensure_database_exists called from thread 3920
2025-05-30 22:35:12.785 - Using existing connection for thread 3920
2025-05-30 22:35:12.787 - Stats database initialized successfully
2025-05-30 22:41:32.135 - ensure_database_exists called from thread 17016
2025-05-30 22:41:32.136 - Creating new thread-specific database connection to data\stats.db for thread 17016
2025-05-30 22:41:32.144 - New database connection created successfully for thread 17016
2025-05-30 22:41:32.152 - Stats database initialized successfully
2025-05-30 22:41:32.156 - ensure_database_exists called from thread 17016
2025-05-30 22:41:32.156 - Using existing connection for thread 17016
2025-05-30 22:41:32.157 - Stats database initialized successfully
2025-05-30 22:41:32.159 - ensure_database_exists called from thread 17016
2025-05-30 22:41:32.159 - Using existing connection for thread 17016
2025-05-30 22:41:32.163 - Stats database initialized successfully
2025-05-30 22:42:04.896 - Using existing connection for thread 17016
2025-05-30 22:42:04.955 - Creating new thread-specific database connection to data\stats.db for thread 5420
2025-05-30 22:42:04.957 - New database connection created successfully for thread 5420
2025-05-30 22:42:04.961 - Database connection closed for thread 5420
2025-05-30 22:42:04.964 - Creating new thread-specific database connection to data\stats.db for thread 5420
2025-05-30 22:42:04.971 - New database connection created successfully for thread 5420
2025-05-30 22:42:05.038 - get_summary_stats called from thread 5420
2025-05-30 22:42:05.041 - Using existing connection for thread 5420
2025-05-30 22:42:05.045 - Total earnings from database: 1300.4
2025-05-30 22:42:05.050 - Daily earnings from database: 608.0
2025-05-30 22:42:05.051 - Daily games from database: 19
2025-05-30 22:42:05.053 - Wallet balance from database: 0
2025-05-30 22:42:05.054 - Total games played from database: 40
2025-05-30 22:42:05.055 - Total winners from database: 40
2025-05-30 22:42:05.057 - Returning summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:42:53.273 - ensure_database_exists called from thread 17016
2025-05-30 22:42:53.274 - Using existing connection for thread 17016
2025-05-30 22:42:53.275 - Stats database initialized successfully
2025-05-30 22:54:44.543 - ensure_database_exists called from thread 9540
2025-05-30 22:54:44.544 - Creating new thread-specific database connection to data\stats.db for thread 9540
2025-05-30 22:54:44.557 - New database connection created successfully for thread 9540
2025-05-30 22:54:44.560 - Stats database initialized successfully
2025-05-30 22:54:44.564 - ensure_database_exists called from thread 9540
2025-05-30 22:54:44.565 - Using existing connection for thread 9540
2025-05-30 22:54:44.566 - Stats database initialized successfully
2025-05-30 22:54:44.567 - ensure_database_exists called from thread 9540
2025-05-30 22:54:44.568 - Using existing connection for thread 9540
2025-05-30 22:54:44.569 - Stats database initialized successfully
2025-05-30 22:55:29.170 - Using existing connection for thread 9540
2025-05-30 22:55:29.230 - Creating new thread-specific database connection to data\stats.db for thread 7696
2025-05-30 22:55:29.232 - New database connection created successfully for thread 7696
2025-05-30 22:55:29.238 - Database connection closed for thread 7696
2025-05-30 22:55:29.240 - Creating new thread-specific database connection to data\stats.db for thread 7696
2025-05-30 22:55:29.242 - New database connection created successfully for thread 7696
2025-05-30 22:55:29.321 - get_summary_stats called from thread 7696
2025-05-30 22:55:29.327 - Using existing connection for thread 7696
2025-05-30 22:55:29.330 - Total earnings from database: 1300.4
2025-05-30 22:55:29.332 - Daily earnings from database: 608.0
2025-05-30 22:55:29.333 - Daily games from database: 19
2025-05-30 22:55:29.334 - Wallet balance from database: 0
2025-05-30 22:55:29.335 - Total games played from database: 40
2025-05-30 22:55:29.336 - Total winners from database: 40
2025-05-30 22:55:29.338 - Returning summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 22:57:58.101 - ensure_database_exists called from thread 9540
2025-05-30 22:57:58.102 - Using existing connection for thread 9540
2025-05-30 22:57:58.103 - Stats database initialized successfully
2025-05-30 23:11:27.582 - ensure_database_exists called from thread 10152
2025-05-30 23:11:27.583 - Creating new thread-specific database connection to data\stats.db for thread 10152
2025-05-30 23:11:27.593 - New database connection created successfully for thread 10152
2025-05-30 23:11:27.596 - Stats database initialized successfully
2025-05-30 23:11:27.600 - ensure_database_exists called from thread 10152
2025-05-30 23:11:27.601 - Using existing connection for thread 10152
2025-05-30 23:11:27.602 - Stats database initialized successfully
2025-05-30 23:11:27.604 - ensure_database_exists called from thread 10152
2025-05-30 23:11:27.605 - Using existing connection for thread 10152
2025-05-30 23:11:27.610 - Stats database initialized successfully
2025-05-30 23:12:01.473 - Using existing connection for thread 10152
2025-05-30 23:12:01.533 - Creating new thread-specific database connection to data\stats.db for thread 16652
2025-05-30 23:12:01.534 - New database connection created successfully for thread 16652
2025-05-30 23:12:01.540 - Database connection closed for thread 16652
2025-05-30 23:12:01.545 - Creating new thread-specific database connection to data\stats.db for thread 16652
2025-05-30 23:12:01.550 - New database connection created successfully for thread 16652
2025-05-30 23:12:01.636 - get_summary_stats called from thread 16652
2025-05-30 23:12:01.637 - Using existing connection for thread 16652
2025-05-30 23:12:01.640 - Total earnings from database: 1300.4
2025-05-30 23:12:01.642 - Daily earnings from database: 608.0
2025-05-30 23:12:01.644 - Daily games from database: 19
2025-05-30 23:12:01.645 - Wallet balance from database: 0
2025-05-30 23:12:01.649 - Total games played from database: 40
2025-05-30 23:12:01.651 - Total winners from database: 40
2025-05-30 23:12:01.653 - Returning summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:21:47.823 - ensure_database_exists called from thread 12912
2025-05-30 23:21:47.823 - Creating new thread-specific database connection to data\stats.db for thread 12912
2025-05-30 23:21:47.833 - New database connection created successfully for thread 12912
2025-05-30 23:21:47.836 - Stats database initialized successfully
2025-05-30 23:21:47.839 - ensure_database_exists called from thread 12912
2025-05-30 23:21:47.841 - Using existing connection for thread 12912
2025-05-30 23:21:47.841 - Stats database initialized successfully
2025-05-30 23:21:47.844 - ensure_database_exists called from thread 12912
2025-05-30 23:21:47.847 - Using existing connection for thread 12912
2025-05-30 23:21:47.848 - Stats database initialized successfully
2025-05-30 23:22:24.102 - Using existing connection for thread 12912
2025-05-30 23:22:24.183 - Creating new thread-specific database connection to data\stats.db for thread 15760
2025-05-30 23:22:24.192 - New database connection created successfully for thread 15760
2025-05-30 23:22:24.212 - Database connection closed for thread 15760
2025-05-30 23:22:24.215 - Creating new thread-specific database connection to data\stats.db for thread 15760
2025-05-30 23:22:24.218 - New database connection created successfully for thread 15760
2025-05-30 23:22:24.295 - get_summary_stats called from thread 15760
2025-05-30 23:22:24.299 - Using existing connection for thread 15760
2025-05-30 23:22:24.309 - Total earnings from database: 1300.4
2025-05-30 23:22:24.311 - Daily earnings from database: 608.0
2025-05-30 23:22:24.314 - Daily games from database: 19
2025-05-30 23:22:24.317 - Wallet balance from database: 0
2025-05-30 23:22:24.319 - Total games played from database: 40
2025-05-30 23:22:24.324 - Total winners from database: 40
2025-05-30 23:22:24.326 - Returning summary stats: {'total_earnings': 1300.4, 'daily_earnings': 608.0, 'daily_games': 19, 'wallet_balance': 0}
2025-05-30 23:22:36.791 - ensure_database_exists called from thread 12912
2025-05-30 23:22:36.792 - Using existing connection for thread 12912
2025-05-30 23:22:36.793 - Stats database initialized successfully
2025-05-30 23:27:50.792 - ensure_database_exists called from thread 7704
2025-05-30 23:27:50.793 - Creating new thread-specific database connection to data\stats.db for thread 7704
2025-05-30 23:27:50.803 - New database connection created successfully for thread 7704
2025-05-30 23:27:50.808 - Stats database initialized successfully
2025-05-30 23:27:50.812 - ensure_database_exists called from thread 7704
2025-05-30 23:27:50.813 - Using existing connection for thread 7704
2025-05-30 23:27:50.814 - Stats database initialized successfully
2025-05-30 23:27:50.815 - ensure_database_exists called from thread 7704
2025-05-30 23:27:50.816 - Using existing connection for thread 7704
2025-05-30 23:27:50.819 - Stats database initialized successfully
