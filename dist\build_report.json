{"build_info": {"timestamp": "2025-05-28 16:34:16", "build_time_seconds": 9815.28, "project_name": "WOW Bingo Game", "project_version": "1.0.0", "executable_path": "D:\\GAME PROJECTS\\LAST-GAME_CONCEPT-\\dist\\WOWBingoGame.exe", "executable_size_mb": 87.09}, "dependencies": {"required_packages": ["pygame", "pyperclip", "json", "datetime", "sqlite3", "os", "sys", "time", "math", "random", "colorsys"], "optional_packages": ["psutil", "rethinkdb", "kivy", "PIL", "numpy", "tkinter"], "missing_packages": []}, "assets": {"verified_directories": ["assets", "data"]}, "system_info": {"platform": "Windows", "python_version": "3.9.18 (main, Sep 11 2023, 13:30:38) [MSC v.1916 64 bit (AMD64)]", "cpu_count": 4}}