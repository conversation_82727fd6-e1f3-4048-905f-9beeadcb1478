[Setup]
AppId={{B8E5F8A1-2C3D-4E5F-6789-ABCDEF123456}}
AppName=WOW Bingo Game
AppVersion=1.0.0
AppVerName=WOW Bingo Game 1.0.0
AppPublisher=WOW Games
AppPublisherURL=https://wowgames.com
AppSupportURL=https://wowgames.com/support
AppUpdatesURL=https://wowgames.com
DefaultDirName={autopf}\WOW Bingo Game
DefaultGroupName=WOW Bingo Game
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=D:\GAME PROJECTS\LAST-GAME_CONCEPT-\installer\output
OutputBaseFilename=WOWBingoGame_Setup_v1.0.0
SetupIconFile=D:\GAME PROJECTS\LAST-GAME_CONCEPT-\installer\assets\app_logo.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=lowest
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "D:\GAME PROJECTS\LAST-GAME_CONCEPT-\installer\WOWBingoGame.exe"; DestDir: "{app}"; Flags: ignoreversion
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{group}\WOW Bingo Game"; Filename: "{app}\WOWBingoGame.exe"
Name: "{group}\{cm:ProgramOnTheWeb,WOW Bingo Game}"; Filename: "https://wowgames.com"
Name: "{group}\{cm:UninstallProgram,WOW Bingo Game}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\WOW Bingo Game"; Filename: "{app}\WOWBingoGame.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\WOW Bingo Game"; Filename: "{app}\WOWBingoGame.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\WOWBingoGame.exe"; Description: "{cm:LaunchProgram,WOW Bingo Game}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Code]
procedure InitializeWizard;
begin
  WizardForm.LicenseAcceptedRadio.Checked := True;
end;
