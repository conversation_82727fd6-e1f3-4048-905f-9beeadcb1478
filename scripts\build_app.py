#!/usr/bin/env python3
"""
WOW Bingo Game - Modern Build Script
===================================

Advanced build script for creating optimized executables using the latest
Python app building tools with high optimization.

Features:
- Multiple build backends (Nuitka, PyInstaller, cx_Freeze)
- Automatic optimization detection
- Asset bundling and compression
- Cross-platform support
- Performance profiling
- Build verification
- Installer creation
"""

import os
import sys
import subprocess
import shutil
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse
import platform

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger


class ModernAppBuilder:
    """Modern application builder with advanced optimization."""
    
    def __init__(self, project_root: Path):
        """Initialize the builder.
        
        Args:
            project_root: Path to project root directory
        """
        self.project_root = project_root
        self.src_dir = project_root / "src"
        self.build_dir = project_root / "build"
        self.dist_dir = project_root / "dist"
        self.assets_dir = project_root / "assets"
        self.data_dir = project_root / "data"
        
        # Build configuration
        self.app_name = "WOWBingoGame"
        self.main_script = "src/wow_bingo_game/main.py"
        self.icon_path = "assets/app_logo.ico"
        self.version = "2.0.0"
        
        # Supported build backends
        self.backends = {
            "nuitka": self._build_with_nuitka,
            "pyinstaller": self._build_with_pyinstaller,
            "cx_freeze": self._build_with_cx_freeze,
            "auto-py-to-exe": self._build_with_auto_py_to_exe
        }
        
        logger.info(f"Modern App Builder initialized for {self.app_name}")
    
    def detect_best_backend(self) -> str:
        """Detect the best available build backend.
        
        Returns:
            Name of the best available backend
        """
        # Priority order: Nuitka > PyInstaller > cx_Freeze > auto-py-to-exe
        priority_order = ["nuitka", "pyinstaller", "cx_freeze", "auto-py-to-exe"]
        
        for backend in priority_order:
            if self._check_backend_available(backend):
                logger.info(f"Selected build backend: {backend}")
                return backend
        
        raise RuntimeError("No suitable build backend found")
    
    def _check_backend_available(self, backend: str) -> bool:
        """Check if a build backend is available.
        
        Args:
            backend: Backend name to check
            
        Returns:
            True if backend is available
        """
        try:
            if backend == "nuitka":
                subprocess.run(["python", "-m", "nuitka", "--version"], 
                             capture_output=True, check=True)
            elif backend == "pyinstaller":
                subprocess.run(["pyinstaller", "--version"], 
                             capture_output=True, check=True)
            elif backend == "cx_freeze":
                import cx_Freeze
            elif backend == "auto-py-to-exe":
                subprocess.run(["auto-py-to-exe", "--version"], 
                             capture_output=True, check=True)
            
            logger.debug(f"Backend {backend} is available")
            return True
            
        except (subprocess.CalledProcessError, ImportError, FileNotFoundError):
            logger.debug(f"Backend {backend} is not available")
            return False
    
    def prepare_build_environment(self) -> None:
        """Prepare the build environment."""
        logger.info("Preparing build environment...")
        
        # Clean previous builds
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        # Create directories
        self.build_dir.mkdir(parents=True, exist_ok=True)
        self.dist_dir.mkdir(parents=True, exist_ok=True)
        
        # Verify main script exists
        if not (self.project_root / self.main_script).exists():
            raise FileNotFoundError(f"Main script not found: {self.main_script}")
        
        # Verify icon exists
        if not (self.project_root / self.icon_path).exists():
            logger.warning(f"Icon not found: {self.icon_path}")
            self.icon_path = None
        
        logger.info("Build environment prepared")
    
    def _build_with_nuitka(self, optimization_level: str = "high") -> bool:
        """Build with Nuitka (recommended for best performance).
        
        Args:
            optimization_level: Optimization level (low, medium, high, extreme)
            
        Returns:
            True if build successful
        """
        logger.info("Building with Nuitka...")
        
        # Base Nuitka arguments
        nuitka_args = [
            "python", "-m", "nuitka",
            "--standalone",
            "--onefile",
            f"--output-filename={self.app_name}.exe",
            f"--output-dir={self.dist_dir}",
            "--assume-yes-for-downloads",
            "--show-progress",
            "--show-memory",
        ]
        
        # Add icon if available
        if self.icon_path:
            nuitka_args.extend([f"--windows-icon-from-ico={self.icon_path}"])
        
        # Optimization settings based on level
        if optimization_level in ["medium", "high", "extreme"]:
            nuitka_args.extend([
                "--lto=yes",  # Link Time Optimization
                "--enable-plugin=anti-bloat",
                "--enable-plugin=data-files",
            ])
        
        if optimization_level in ["high", "extreme"]:
            nuitka_args.extend([
                "--clang",  # Use Clang if available
                "--msvc=latest",  # Use latest MSVC on Windows
                "--jobs=0",  # Use all CPU cores
            ])
        
        if optimization_level == "extreme":
            nuitka_args.extend([
                "--lto=yes",
                "--enable-plugin=numpy",
                "--enable-plugin=multiprocessing",
            ])
        
        # Include data directories
        data_dirs = [
            f"--include-data-dir={self.assets_dir}=assets",
            f"--include-data-dir={self.data_dir}=data",
        ]
        nuitka_args.extend(data_dirs)
        
        # Include packages
        packages = [
            "--include-package=flet",
            "--include-package=pygame",
            "--include-package=loguru",
            "--include-package=pydantic",
            "--include-package=PIL",
            "--include-package=numpy",
        ]
        nuitka_args.extend(packages)
        
        # Disable console on Windows
        if platform.system() == "Windows":
            nuitka_args.append("--disable-console")
        
        # Add main script
        nuitka_args.append(str(self.project_root / self.main_script))
        
        try:
            logger.info(f"Running Nuitka with {optimization_level} optimization...")
            result = subprocess.run(nuitka_args, cwd=self.project_root, check=True)
            logger.info("Nuitka build completed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Nuitka build failed: {e}")
            return False
    
    def _build_with_pyinstaller(self, optimization_level: str = "medium") -> bool:
        """Build with PyInstaller.
        
        Args:
            optimization_level: Optimization level
            
        Returns:
            True if build successful
        """
        logger.info("Building with PyInstaller...")
        
        # PyInstaller arguments
        pyinstaller_args = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            f"--name={self.app_name}",
            f"--distpath={self.dist_dir}",
            f"--workpath={self.build_dir}",
            f"--specpath={self.build_dir}",
        ]
        
        # Add icon if available
        if self.icon_path:
            pyinstaller_args.extend([f"--icon={self.icon_path}"])
        
        # Add data directories
        pyinstaller_args.extend([
            f"--add-data={self.assets_dir};assets",
            f"--add-data={self.data_dir};data",
        ])
        
        # Optimization settings
        if optimization_level in ["medium", "high", "extreme"]:
            pyinstaller_args.extend([
                "--optimize=2",
                "--strip",
            ])
        
        # Add main script
        pyinstaller_args.append(str(self.project_root / self.main_script))
        
        try:
            logger.info(f"Running PyInstaller with {optimization_level} optimization...")
            result = subprocess.run(pyinstaller_args, cwd=self.project_root, check=True)
            logger.info("PyInstaller build completed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"PyInstaller build failed: {e}")
            return False
    
    def _build_with_cx_freeze(self, optimization_level: str = "medium") -> bool:
        """Build with cx_Freeze.
        
        Args:
            optimization_level: Optimization level
            
        Returns:
            True if build successful
        """
        logger.info("Building with cx_Freeze...")
        
        # Create setup script for cx_Freeze
        setup_script = self._create_cx_freeze_setup(optimization_level)
        setup_path = self.build_dir / "setup_cx_freeze.py"
        
        with open(setup_path, 'w') as f:
            f.write(setup_script)
        
        try:
            logger.info(f"Running cx_Freeze with {optimization_level} optimization...")
            result = subprocess.run([
                "python", str(setup_path), "build_exe"
            ], cwd=self.project_root, check=True)
            logger.info("cx_Freeze build completed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"cx_Freeze build failed: {e}")
            return False
    
    def _build_with_auto_py_to_exe(self, optimization_level: str = "medium") -> bool:
        """Build with auto-py-to-exe.
        
        Args:
            optimization_level: Optimization level
            
        Returns:
            True if build successful
        """
        logger.info("Building with auto-py-to-exe...")
        
        # Create configuration for auto-py-to-exe
        config = self._create_auto_py_to_exe_config(optimization_level)
        config_path = self.build_dir / "auto_py_to_exe_config.json"
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        try:
            logger.info(f"Running auto-py-to-exe with {optimization_level} optimization...")
            result = subprocess.run([
                "auto-py-to-exe", "--config", str(config_path)
            ], cwd=self.project_root, check=True)
            logger.info("auto-py-to-exe build completed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"auto-py-to-exe build failed: {e}")
            return False
    
    def _create_cx_freeze_setup(self, optimization_level: str) -> str:
        """Create setup script for cx_Freeze."""
        return f'''
import sys
from cx_Freeze import setup, Executable

# Dependencies
build_exe_options = {{
    "packages": ["flet", "pygame", "loguru", "pydantic", "PIL", "numpy"],
    "excludes": ["tkinter", "unittest", "email", "http", "urllib", "xml"],
    "include_files": [
        ("{self.assets_dir}", "assets"),
        ("{self.data_dir}", "data"),
    ],
    "optimize": 2 if "{optimization_level}" in ["medium", "high", "extreme"] else 0,
}}

# Base for Windows
base = None
if sys.platform == "win32":
    base = "Win32GUI"

setup(
    name="{self.app_name}",
    version="{self.version}",
    description="WOW Bingo Game",
    options={{"build_exe": build_exe_options}},
    executables=[Executable(
        "{self.main_script}",
        base=base,
        icon="{self.icon_path}" if "{self.icon_path}" else None,
        target_name="{self.app_name}.exe"
    )]
)
'''
    
    def _create_auto_py_to_exe_config(self, optimization_level: str) -> dict:
        """Create configuration for auto-py-to-exe."""
        return {
            "version": "auto-py-to-exe-configuration_v1",
            "pyinstallerOptions": [
                {
                    "optionDest": "filenames",
                    "value": str(self.project_root / self.main_script)
                },
                {
                    "optionDest": "onefile",
                    "value": True
                },
                {
                    "optionDest": "console",
                    "value": False
                },
                {
                    "optionDest": "icon_file",
                    "value": str(self.project_root / self.icon_path) if self.icon_path else ""
                },
                {
                    "optionDest": "name",
                    "value": self.app_name
                },
                {
                    "optionDest": "distpath",
                    "value": str(self.dist_dir)
                }
            ]
        }
    
    def verify_build(self) -> bool:
        """Verify the build was successful.
        
        Returns:
            True if build verification passed
        """
        logger.info("Verifying build...")
        
        # Check if executable exists
        exe_name = f"{self.app_name}.exe" if platform.system() == "Windows" else self.app_name
        exe_path = self.dist_dir / exe_name
        
        if not exe_path.exists():
            logger.error(f"Executable not found: {exe_path}")
            return False
        
        # Check file size (should be reasonable)
        file_size_mb = exe_path.stat().st_size / (1024 * 1024)
        logger.info(f"Executable size: {file_size_mb:.2f} MB")
        
        if file_size_mb < 10:  # Too small, probably missing dependencies
            logger.warning("Executable seems too small, may be missing dependencies")
        elif file_size_mb > 500:  # Too large
            logger.warning("Executable is very large, consider optimization")
        
        logger.info("Build verification completed")
        return True
    
    def create_installer(self) -> bool:
        """Create an installer for the application.
        
        Returns:
            True if installer creation successful
        """
        logger.info("Creating installer...")
        
        try:
            # Use NSIS or Inno Setup for Windows installer
            if platform.system() == "Windows":
                return self._create_windows_installer()
            else:
                logger.info("Installer creation not implemented for this platform")
                return True
                
        except Exception as e:
            logger.error(f"Installer creation failed: {e}")
            return False
    
    def _create_windows_installer(self) -> bool:
        """Create Windows installer using Inno Setup."""
        # This would create an Inno Setup script and compile it
        # For now, just log that it would be created
        logger.info("Windows installer would be created here")
        return True
    
    def build(self, backend: Optional[str] = None, optimization: str = "high") -> bool:
        """Build the application.
        
        Args:
            backend: Build backend to use (auto-detect if None)
            optimization: Optimization level
            
        Returns:
            True if build successful
        """
        try:
            start_time = time.time()
            logger.info(f"Starting build process with {optimization} optimization...")
            
            # Prepare environment
            self.prepare_build_environment()
            
            # Select backend
            if backend is None:
                backend = self.detect_best_backend()
            elif backend not in self.backends:
                raise ValueError(f"Unknown backend: {backend}")
            
            # Build with selected backend
            build_func = self.backends[backend]
            success = build_func(optimization)
            
            if not success:
                logger.error("Build failed")
                return False
            
            # Verify build
            if not self.verify_build():
                logger.error("Build verification failed")
                return False
            
            # Create installer
            self.create_installer()
            
            elapsed_time = time.time() - start_time
            logger.info(f"Build completed successfully in {elapsed_time:.2f} seconds")
            
            return True
            
        except Exception as e:
            logger.error(f"Build process failed: {e}")
            return False


def main():
    """Main entry point for the build script."""
    parser = argparse.ArgumentParser(description="WOW Bingo Game - Modern Build Script")
    parser.add_argument("--backend", choices=["nuitka", "pyinstaller", "cx_freeze", "auto-py-to-exe"],
                       help="Build backend to use (auto-detect if not specified)")
    parser.add_argument("--optimization", choices=["low", "medium", "high", "extreme"],
                       default="high", help="Optimization level")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    logger.remove()
    logger.add(sys.stderr, level=log_level, format="{time} | {level} | {message}")
    
    try:
        # Create builder
        builder = ModernAppBuilder(project_root)
        
        # Build application
        success = builder.build(backend=args.backend, optimization=args.optimization)
        
        if success:
            logger.info("🎉 Build completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Build failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
