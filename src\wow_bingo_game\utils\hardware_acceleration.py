"""
WOW Bingo Game - Hardware Acceleration Detection and Optimization
================================================================

Advanced hardware acceleration detection with QSV (Quick Sync Video) GPU
priority and intelligent fallback to CPU optimization.

Features:
- Intel QSV (Quick Sync Video) detection and optimization
- NVIDIA NVENC detection
- AMD VCE detection
- CPU-based optimization fallback
- Automatic performance profiling
- Dynamic optimization adjustment
- Hardware-specific rendering optimizations
"""

import asyncio
import os
import platform
import subprocess
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from loguru import logger

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False


class AccelerationType(Enum):
    """Hardware acceleration types in priority order."""
    QSV = "Intel Quick Sync Video"
    NVENC = "NVIDIA NVENC"
    VCE = "AMD VCE"
    CPU_OPTIMIZED = "CPU Optimized"
    CPU_BASIC = "CPU Basic"


@dataclass
class HardwareInfo:
    """Hardware information structure."""
    cpu_brand: str = "Unknown"
    cpu_cores: int = 1
    cpu_threads: int = 1
    cpu_base_freq: float = 0.0
    cpu_max_freq: float = 0.0
    memory_total_gb: float = 0.0
    gpu_devices: List[Dict[str, Any]] = None
    intel_gpu_present: bool = False
    nvidia_gpu_present: bool = False
    amd_gpu_present: bool = False
    qsv_available: bool = False
    nvenc_available: bool = False
    vce_available: bool = False

    def __post_init__(self):
        if self.gpu_devices is None:
            self.gpu_devices = []


@dataclass
class OptimizationProfile:
    """Hardware optimization profile."""
    profile_name: str
    acceleration_type: AccelerationType
    render_quality: str  # "ultra", "high", "medium", "low"
    animation_quality: str  # "ultra", "high", "medium", "low"
    texture_quality: str  # "ultra", "high", "medium", "low"
    vsync_enabled: bool
    max_fps: int
    memory_optimization: str  # "aggressive", "balanced", "conservative"
    cpu_threads: int
    gpu_acceleration: bool
    hardware_decoding: bool
    batch_rendering: bool
    cache_size_mb: int
    performance_monitoring: bool


class HardwareAccelerationManager:
    """Manages hardware acceleration detection and optimization."""

    def __init__(self):
        """Initialize hardware acceleration manager."""
        self.hardware_info = HardwareInfo()
        self.optimization_profile: Optional[OptimizationProfile] = None
        self.initialized = False
        self.detection_cache = {}

        logger.info("Hardware acceleration manager created")

    async def initialize(self) -> bool:
        """Initialize hardware detection and optimization.

        Returns:
            True if initialization successful
        """
        try:
            logger.info("Detecting hardware acceleration capabilities...")

            # Detect hardware information
            await self._detect_hardware_info()

            # Detect acceleration capabilities in priority order
            await self._detect_qsv_acceleration()
            await self._detect_nvenc_acceleration()
            await self._detect_vce_acceleration()

            # Create optimization profile
            self.optimization_profile = self._create_optimization_profile()

            # Log detection results
            self._log_hardware_summary()

            self.initialized = True
            logger.info("Hardware acceleration initialization completed")
            return True

        except Exception as e:
            logger.error(f"Hardware acceleration initialization failed: {e}")
            # Create fallback CPU profile
            self.optimization_profile = self._create_cpu_fallback_profile()
            return False

    async def _detect_hardware_info(self) -> None:
        """Detect basic hardware information."""
        try:
            # CPU information
            self.hardware_info.cpu_brand = platform.processor() or "Unknown"
            self.hardware_info.cpu_cores = os.cpu_count() or 1

            if PSUTIL_AVAILABLE:
                # Enhanced CPU info with psutil
                self.hardware_info.cpu_threads = psutil.cpu_count(logical=True) or 1

                # CPU frequency information
                cpu_freq = psutil.cpu_freq()
                if cpu_freq:
                    self.hardware_info.cpu_base_freq = cpu_freq.current or 0.0
                    self.hardware_info.cpu_max_freq = cpu_freq.max or 0.0

                # Memory information
                memory = psutil.virtual_memory()
                self.hardware_info.memory_total_gb = memory.total / (1024**3)
            else:
                self.hardware_info.cpu_threads = self.hardware_info.cpu_cores

            # GPU detection
            await self._detect_gpu_devices()

            logger.info(f"Hardware detected: {self.hardware_info.cpu_brand}, "
                       f"{self.hardware_info.cpu_cores}C/{self.hardware_info.cpu_threads}T, "
                       f"{self.hardware_info.memory_total_gb:.1f}GB RAM")

        except Exception as e:
            logger.error(f"Hardware detection failed: {e}")

    async def _detect_gpu_devices(self) -> None:
        """Detect GPU devices and capabilities."""
        try:
            gpu_devices = []

            # Windows GPU detection
            if platform.system() == "Windows":
                gpu_devices = await self._detect_windows_gpus()
            # Linux GPU detection
            elif platform.system() == "Linux":
                gpu_devices = await self._detect_linux_gpus()
            # macOS GPU detection
            elif platform.system() == "Darwin":
                gpu_devices = await self._detect_macos_gpus()

            self.hardware_info.gpu_devices = gpu_devices

            # Analyze GPU vendors
            for gpu in gpu_devices:
                gpu_name = gpu.get("name", "").lower()
                if "intel" in gpu_name:
                    self.hardware_info.intel_gpu_present = True
                elif "nvidia" in gpu_name or "geforce" in gpu_name or "quadro" in gpu_name:
                    self.hardware_info.nvidia_gpu_present = True
                elif "amd" in gpu_name or "radeon" in gpu_name:
                    self.hardware_info.amd_gpu_present = True

            logger.info(f"GPU devices detected: {len(gpu_devices)}")
            for gpu in gpu_devices:
                logger.info(f"  - {gpu.get('name', 'Unknown')}")

        except Exception as e:
            logger.error(f"GPU detection failed: {e}")

    async def _detect_windows_gpus(self) -> List[Dict[str, Any]]:
        """Detect GPU devices on Windows."""
        gpu_devices = []

        try:
            # Try WMI first (most reliable on Windows)
            if WMI_AVAILABLE:
                gpu_devices = await self._detect_windows_gpus_wmi()

            # Fallback to wmic command
            if not gpu_devices:
                gpu_devices = await self._detect_windows_gpus_wmic()

        except Exception as e:
            logger.debug(f"Windows GPU detection error: {e}")

        return gpu_devices

    async def _detect_windows_gpus_wmi(self) -> List[Dict[str, Any]]:
        """Detect Windows GPUs using WMI."""
        gpu_devices = []

        try:
            c = wmi.WMI()
            for gpu in c.Win32_VideoController():
                if gpu.Name:
                    gpu_info = {
                        "name": gpu.Name,
                        "driver_version": gpu.DriverVersion or "Unknown",
                        "memory_mb": int(gpu.AdapterRAM / (1024*1024)) if gpu.AdapterRAM else 0,
                        "vendor": self._extract_gpu_vendor(gpu.Name),
                        "pci_device_id": gpu.PNPDeviceID or "Unknown"
                    }
                    gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"WMI GPU detection failed: {e}")

        return gpu_devices

    async def _detect_windows_gpus_wmic(self) -> List[Dict[str, Any]]:
        """Detect Windows GPUs using wmic command."""
        gpu_devices = []

        try:
            result = await asyncio.create_subprocess_exec(
                "wmic", "path", "win32_VideoController",
                "get", "name,driverversion,adapterram", "/format:csv",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                lines = stdout.decode('utf-8', errors='ignore').split('\n')
                for line in lines[1:]:  # Skip header
                    if line.strip() and ',' in line:
                        parts = line.split(',')
                        if len(parts) >= 4 and parts[2].strip():
                            gpu_info = {
                                "name": parts[2].strip(),
                                "driver_version": parts[1].strip() or "Unknown",
                                "memory_mb": int(parts[0]) // (1024*1024) if parts[0].strip().isdigit() else 0,
                                "vendor": self._extract_gpu_vendor(parts[2].strip()),
                                "pci_device_id": "Unknown"
                            }
                            gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"WMIC GPU detection failed: {e}")

        return gpu_devices

    async def _detect_linux_gpus(self) -> List[Dict[str, Any]]:
        """Detect GPU devices on Linux."""
        gpu_devices = []

        try:
            # Use lspci to detect GPUs
            result = await asyncio.create_subprocess_exec(
                "lspci", "-nn",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                lines = stdout.decode('utf-8', errors='ignore').split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in ['vga', 'display', '3d']):
                        gpu_name = line.split(': ', 1)[1] if ': ' in line else line
                        gpu_info = {
                            "name": gpu_name,
                            "driver_version": "Unknown",
                            "memory_mb": 0,
                            "vendor": self._extract_gpu_vendor(gpu_name),
                            "pci_device_id": "Unknown"
                        }
                        gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"Linux GPU detection failed: {e}")

        return gpu_devices

    async def _detect_macos_gpus(self) -> List[Dict[str, Any]]:
        """Detect GPU devices on macOS."""
        gpu_devices = []

        try:
            # Use system_profiler to detect GPUs
            result = await asyncio.create_subprocess_exec(
                "system_profiler", "SPDisplaysDataType",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                # Basic parsing - could be enhanced
                if "Chipset Model:" in stdout.decode('utf-8', errors='ignore'):
                    gpu_info = {
                        "name": "macOS GPU (detected)",
                        "driver_version": "Unknown",
                        "memory_mb": 0,
                        "vendor": "Apple/Intel",
                        "pci_device_id": "Unknown"
                    }
                    gpu_devices.append(gpu_info)

        except Exception as e:
            logger.debug(f"macOS GPU detection failed: {e}")

        return gpu_devices

    def _extract_gpu_vendor(self, gpu_name: str) -> str:
        """Extract GPU vendor from name."""
        gpu_name_lower = gpu_name.lower()
        if "intel" in gpu_name_lower:
            return "Intel"
        elif any(keyword in gpu_name_lower for keyword in ["nvidia", "geforce", "quadro", "tesla"]):
            return "NVIDIA"
        elif any(keyword in gpu_name_lower for keyword in ["amd", "radeon", "ati"]):
            return "AMD"
        else:
            return "Unknown"

    async def _detect_qsv_acceleration(self) -> None:
        """Detect Intel Quick Sync Video (QSV) acceleration."""
        try:
            logger.info("Detecting Intel QSV acceleration...")

            # Check if Intel GPU is present
            if not self.hardware_info.intel_gpu_present:
                logger.info("No Intel GPU detected, QSV not available")
                return

            # Check CPU for Intel Quick Sync support
            cpu_brand = self.hardware_info.cpu_brand.lower()
            qsv_supported_families = [
                "sandy bridge", "ivy bridge", "haswell", "broadwell",
                "skylake", "kaby lake", "coffee lake", "whiskey lake",
                "comet lake", "ice lake", "tiger lake", "rocket lake",
                "alder lake", "raptor lake"
            ]

            # Check if CPU supports QSV
            cpu_supports_qsv = any(family in cpu_brand for family in qsv_supported_families)

            # Additional check for Intel processors
            if "intel" in cpu_brand:
                cpu_supports_qsv = True

            if cpu_supports_qsv:
                # Try to verify QSV availability through system calls
                qsv_available = await self._verify_qsv_availability()
                self.hardware_info.qsv_available = qsv_available

                if qsv_available:
                    logger.info("✅ Intel QSV acceleration detected and available")
                else:
                    logger.info("⚠️ Intel QSV may be supported but not currently available")
            else:
                logger.info("CPU does not support Intel QSV")

        except Exception as e:
            logger.error(f"QSV detection failed: {e}")

    async def _verify_qsv_availability(self) -> bool:
        """Verify QSV availability through system checks."""
        try:
            # On Windows, check for Intel Media SDK or Intel Graphics drivers
            if platform.system() == "Windows":
                return await self._verify_qsv_windows()
            # On Linux, check for Intel VAAPI drivers
            elif platform.system() == "Linux":
                return await self._verify_qsv_linux()
            else:
                return False

        except Exception as e:
            logger.debug(f"QSV verification failed: {e}")
            return False

    async def _verify_qsv_windows(self) -> bool:
        """Verify QSV on Windows."""
        try:
            # Check for Intel Graphics drivers in registry or system
            # This is a simplified check - could be enhanced
            intel_driver_paths = [
                "C:\\Windows\\System32\\igdumdim64.dll",
                "C:\\Windows\\System32\\igdumdim32.dll",
                "C:\\Windows\\System32\\DriverStore\\FileRepository\\*intel*"
            ]

            for path in intel_driver_paths:
                if os.path.exists(path):
                    logger.debug(f"Intel driver found: {path}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"Windows QSV verification failed: {e}")
            return False

    async def _verify_qsv_linux(self) -> bool:
        """Verify QSV on Linux."""
        try:
            # Check for VAAPI devices
            vaapi_devices = ["/dev/dri/renderD128", "/dev/dri/card0"]
            for device in vaapi_devices:
                if os.path.exists(device):
                    logger.debug(f"VAAPI device found: {device}")
                    return True

            return False

        except Exception as e:
            logger.debug(f"Linux QSV verification failed: {e}")
            return False

    async def _detect_nvenc_acceleration(self) -> None:
        """Detect NVIDIA NVENC acceleration."""
        try:
            logger.info("Detecting NVIDIA NVENC acceleration...")

            if not self.hardware_info.nvidia_gpu_present:
                logger.info("No NVIDIA GPU detected, NVENC not available")
                return

            # Check for NVENC support (most modern NVIDIA GPUs support it)
            nvenc_available = await self._verify_nvenc_availability()
            self.hardware_info.nvenc_available = nvenc_available

            if nvenc_available:
                logger.info("✅ NVIDIA NVENC acceleration detected and available")
            else:
                logger.info("⚠️ NVIDIA GPU present but NVENC not available")

        except Exception as e:
            logger.error(f"NVENC detection failed: {e}")

    async def _verify_nvenc_availability(self) -> bool:
        """Verify NVENC availability."""
        try:
            # Check for NVIDIA drivers
            if platform.system() == "Windows":
                nvidia_paths = [
                    "C:\\Windows\\System32\\nvencodeapi64.dll",
                    "C:\\Windows\\System32\\nvencodeapi.dll"
                ]
                for path in nvidia_paths:
                    if os.path.exists(path):
                        return True
            elif platform.system() == "Linux":
                # Check for NVIDIA drivers
                nvidia_paths = ["/usr/lib/x86_64-linux-gnu/libnvidia-encode.so"]
                for path in nvidia_paths:
                    if os.path.exists(path):
                        return True

            return False

        except Exception as e:
            logger.debug(f"NVENC verification failed: {e}")
            return False

    async def _detect_vce_acceleration(self) -> None:
        """Detect AMD VCE acceleration."""
        try:
            logger.info("Detecting AMD VCE acceleration...")

            if not self.hardware_info.amd_gpu_present:
                logger.info("No AMD GPU detected, VCE not available")
                return

            # Check for VCE support
            vce_available = await self._verify_vce_availability()
            self.hardware_info.vce_available = vce_available

            if vce_available:
                logger.info("✅ AMD VCE acceleration detected and available")
            else:
                logger.info("⚠️ AMD GPU present but VCE not available")

        except Exception as e:
            logger.error(f"VCE detection failed: {e}")

    async def _verify_vce_availability(self) -> bool:
        """Verify VCE availability."""
        try:
            # Basic check for AMD drivers
            if platform.system() == "Windows":
                # Check for AMD drivers
                amd_paths = ["C:\\Windows\\System32\\amfrt64.dll"]
                for path in amd_paths:
                    if os.path.exists(path):
                        return True
            elif platform.system() == "Linux":
                # Check for AMD drivers
                amd_paths = ["/usr/lib/x86_64-linux-gnu/libamfrt64.so"]
                for path in amd_paths:
                    if os.path.exists(path):
                        return True

            return False

        except Exception as e:
            logger.debug(f"VCE verification failed: {e}")
            return False

    def _create_optimization_profile(self) -> OptimizationProfile:
        """Create hardware-specific optimization profile."""
        try:
            # Determine best acceleration type in priority order
            if self.hardware_info.qsv_available:
                return self._create_qsv_profile()
            elif self.hardware_info.nvenc_available:
                return self._create_nvenc_profile()
            elif self.hardware_info.vce_available:
                return self._create_vce_profile()
            else:
                return self._create_cpu_optimized_profile()

        except Exception as e:
            logger.error(f"Failed to create optimization profile: {e}")
            return self._create_cpu_fallback_profile()

    def _create_qsv_profile(self) -> OptimizationProfile:
        """Create Intel QSV optimized profile."""
        return OptimizationProfile(
            profile_name="Intel QSV Optimized",
            acceleration_type=AccelerationType.QSV,
            render_quality="ultra",
            animation_quality="ultra",
            texture_quality="high",
            vsync_enabled=True,
            max_fps=120,
            memory_optimization="balanced",
            cpu_threads=min(self.hardware_info.cpu_threads, 8),
            gpu_acceleration=True,
            hardware_decoding=True,
            batch_rendering=True,
            cache_size_mb=256,
            performance_monitoring=True
        )

    def _create_nvenc_profile(self) -> OptimizationProfile:
        """Create NVIDIA NVENC optimized profile."""
        return OptimizationProfile(
            profile_name="NVIDIA NVENC Optimized",
            acceleration_type=AccelerationType.NVENC,
            render_quality="ultra",
            animation_quality="ultra",
            texture_quality="ultra",
            vsync_enabled=True,
            max_fps=144,
            memory_optimization="conservative",
            cpu_threads=min(self.hardware_info.cpu_threads, 12),
            gpu_acceleration=True,
            hardware_decoding=True,
            batch_rendering=True,
            cache_size_mb=512,
            performance_monitoring=True
        )

    def _create_vce_profile(self) -> OptimizationProfile:
        """Create AMD VCE optimized profile."""
        return OptimizationProfile(
            profile_name="AMD VCE Optimized",
            acceleration_type=AccelerationType.VCE,
            render_quality="high",
            animation_quality="high",
            texture_quality="high",
            vsync_enabled=True,
            max_fps=120,
            memory_optimization="balanced",
            cpu_threads=min(self.hardware_info.cpu_threads, 10),
            gpu_acceleration=True,
            hardware_decoding=True,
            batch_rendering=True,
            cache_size_mb=384,
            performance_monitoring=True
        )

    def _create_cpu_optimized_profile(self) -> OptimizationProfile:
        """Create CPU-optimized profile for systems without GPU acceleration."""
        # Determine CPU performance tier
        cpu_cores = self.hardware_info.cpu_cores
        memory_gb = self.hardware_info.memory_total_gb

        if cpu_cores >= 8 and memory_gb >= 16:
            # High-end CPU
            return OptimizationProfile(
                profile_name="High-End CPU Optimized",
                acceleration_type=AccelerationType.CPU_OPTIMIZED,
                render_quality="high",
                animation_quality="high",
                texture_quality="medium",
                vsync_enabled=True,
                max_fps=60,
                memory_optimization="conservative",
                cpu_threads=min(cpu_cores, 6),
                gpu_acceleration=False,
                hardware_decoding=False,
                batch_rendering=True,
                cache_size_mb=256,
                performance_monitoring=True
            )
        elif cpu_cores >= 4 and memory_gb >= 8:
            # Mid-range CPU
            return OptimizationProfile(
                profile_name="Mid-Range CPU Optimized",
                acceleration_type=AccelerationType.CPU_OPTIMIZED,
                render_quality="medium",
                animation_quality="medium",
                texture_quality="medium",
                vsync_enabled=True,
                max_fps=60,
                memory_optimization="balanced",
                cpu_threads=min(cpu_cores, 4),
                gpu_acceleration=False,
                hardware_decoding=False,
                batch_rendering=True,
                cache_size_mb=128,
                performance_monitoring=True
            )
        else:
            # Low-end CPU
            return self._create_cpu_fallback_profile()

    def _create_cpu_fallback_profile(self) -> OptimizationProfile:
        """Create basic CPU fallback profile for older/limited hardware."""
        return OptimizationProfile(
            profile_name="CPU Basic (Fallback)",
            acceleration_type=AccelerationType.CPU_BASIC,
            render_quality="low",
            animation_quality="low",
            texture_quality="low",
            vsync_enabled=False,
            max_fps=30,
            memory_optimization="aggressive",
            cpu_threads=min(self.hardware_info.cpu_threads, 2),
            gpu_acceleration=False,
            hardware_decoding=False,
            batch_rendering=False,
            cache_size_mb=64,
            performance_monitoring=False
        )

    def _log_hardware_summary(self) -> None:
        """Log comprehensive hardware detection summary."""
        logger.info("=== Hardware Acceleration Summary ===")
        logger.info(f"CPU: {self.hardware_info.cpu_brand}")
        logger.info(f"Cores/Threads: {self.hardware_info.cpu_cores}/{self.hardware_info.cpu_threads}")
        logger.info(f"Memory: {self.hardware_info.memory_total_gb:.1f}GB")
        logger.info(f"GPU Devices: {len(self.hardware_info.gpu_devices)}")

        for gpu in self.hardware_info.gpu_devices:
            logger.info(f"  - {gpu['name']} ({gpu['vendor']})")

        logger.info("Acceleration Support:")
        logger.info(f"  Intel QSV: {'✅ Available' if self.hardware_info.qsv_available else '❌ Not Available'}")
        logger.info(f"  NVIDIA NVENC: {'✅ Available' if self.hardware_info.nvenc_available else '❌ Not Available'}")
        logger.info(f"  AMD VCE: {'✅ Available' if self.hardware_info.vce_available else '❌ Not Available'}")

        if self.optimization_profile:
            logger.info(f"Selected Profile: {self.optimization_profile.profile_name}")
            logger.info(f"Acceleration Type: {self.optimization_profile.acceleration_type.value}")
            logger.info(f"Render Quality: {self.optimization_profile.render_quality}")
            logger.info(f"Max FPS: {self.optimization_profile.max_fps}")

    def get_optimization_profile(self) -> Dict[str, Any]:
        """Get current optimization profile as dictionary.

        Returns:
            Dictionary with optimization settings
        """
        if not self.optimization_profile:
            return {}

        return {
            "profile_name": self.optimization_profile.profile_name,
            "acceleration_type": self.optimization_profile.acceleration_type.value,
            "render_quality": self.optimization_profile.render_quality,
            "animation_quality": self.optimization_profile.animation_quality,
            "texture_quality": self.optimization_profile.texture_quality,
            "vsync_enabled": self.optimization_profile.vsync_enabled,
            "max_fps": self.optimization_profile.max_fps,
            "memory_optimization": self.optimization_profile.memory_optimization,
            "cpu_threads": self.optimization_profile.cpu_threads,
            "gpu_acceleration": self.optimization_profile.gpu_acceleration,
            "hardware_decoding": self.optimization_profile.hardware_decoding,
            "batch_rendering": self.optimization_profile.batch_rendering,
            "cache_size_mb": self.optimization_profile.cache_size_mb,
            "performance_monitoring": self.optimization_profile.performance_monitoring,
            "hardware_info": {
                "qsv_available": self.hardware_info.qsv_available,
                "nvenc_available": self.hardware_info.nvenc_available,
                "vce_available": self.hardware_info.vce_available,
                "cpu_cores": self.hardware_info.cpu_cores,
                "memory_gb": self.hardware_info.memory_total_gb
            }
        }

    def get_hardware_info(self) -> Dict[str, Any]:
        """Get detailed hardware information.

        Returns:
            Dictionary with hardware details
        """
        return {
            "cpu_brand": self.hardware_info.cpu_brand,
            "cpu_cores": self.hardware_info.cpu_cores,
            "cpu_threads": self.hardware_info.cpu_threads,
            "memory_total_gb": self.hardware_info.memory_total_gb,
            "gpu_devices": self.hardware_info.gpu_devices,
            "acceleration_support": {
                "qsv": self.hardware_info.qsv_available,
                "nvenc": self.hardware_info.nvenc_available,
                "vce": self.hardware_info.vce_available
            }
        }
