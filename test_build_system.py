#!/usr/bin/env python3
"""
Test Script for WOW Bingo Game Build System
===========================================

This script tests the build system components to ensure everything
is working correctly before attempting a full build.

Usage:
    python test_build_system.py [--verbose]
"""

import sys
import os
import importlib
import subprocess
from pathlib import Path

def log(message, level="INFO"):
    """Log a message with level."""
    print(f"[{level}] {message}")

def test_python_environment():
    """Test Python environment and version."""
    log("Testing Python environment...")
    
    # Check Python version
    version = sys.version_info
    if version < (3, 7):
        log(f"Python version too old: {version.major}.{version.minor}.{version.micro}", "ERROR")
        return False
    
    log(f"Python version: {version.major}.{version.minor}.{version.micro} ✓")
    
    # Check pip
    try:
        import pip
        log("pip available ✓")
    except ImportError:
        log("pip not available", "ERROR")
        return False
    
    return True

def test_required_packages():
    """Test if required packages are available."""
    log("Testing required packages...")
    
    required_packages = [
        'pygame',
        'pyperclip',
        'nuitka'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            log(f"Package '{package}' available ✓")
        except ImportError:
            log(f"Package '{package}' missing", "ERROR")
            missing_packages.append(package)
    
    if missing_packages:
        log(f"Missing packages: {', '.join(missing_packages)}", "ERROR")
        log("Run: python setup_build_environment.py", "INFO")
        return False
    
    return True

def test_nuitka_functionality():
    """Test Nuitka basic functionality."""
    log("Testing Nuitka functionality...")
    
    try:
        result = subprocess.run([sys.executable, '-m', 'nuitka', '--version'], 
                              capture_output=True, text=True, check=True)
        nuitka_version = result.stdout.strip()
        log(f"Nuitka version: {nuitka_version} ✓")
        
        # Test help command
        result = subprocess.run([sys.executable, '-m', 'nuitka', '--help'], 
                              capture_output=True, text=True, check=True)
        if 'standalone' in result.stdout:
            log("Nuitka standalone mode available ✓")
        else:
            log("Nuitka standalone mode not available", "WARNING")
        
        return True
        
    except subprocess.CalledProcessError as e:
        log(f"Nuitka test failed: {e}", "ERROR")
        return False

def test_project_structure():
    """Test project structure and required files."""
    log("Testing project structure...")
    
    project_root = Path(__file__).parent
    
    # Required files
    required_files = [
        'main.py',
        'nuitka_comprehensive_build.py',
        'build_requirements.txt'
    ]
    
    # Required directories
    required_dirs = [
        'assets',
        'data'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_name in required_files:
        file_path = project_root / file_name
        if file_path.exists():
            log(f"Required file '{file_name}' found ✓")
        else:
            log(f"Required file '{file_name}' missing", "ERROR")
            missing_files.append(file_name)
    
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists() and dir_path.is_dir():
            log(f"Required directory '{dir_name}' found ✓")
        else:
            log(f"Required directory '{dir_name}' missing", "ERROR")
            missing_dirs.append(dir_name)
    
    if missing_files or missing_dirs:
        log("Project structure incomplete", "ERROR")
        return False
    
    return True

def test_build_script_syntax():
    """Test build script syntax."""
    log("Testing build script syntax...")
    
    build_script = Path(__file__).parent / "nuitka_comprehensive_build.py"
    
    try:
        # Try to compile the script
        with open(build_script, 'r') as f:
            code = f.read()
        
        compile(code, str(build_script), 'exec')
        log("Build script syntax valid ✓")
        
        # Try to import the module
        spec = importlib.util.spec_from_file_location("build_module", build_script)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Check if main class exists
        if hasattr(module, 'NuitkaBuildSystem'):
            log("Build system class found ✓")
        else:
            log("Build system class not found", "ERROR")
            return False
        
        return True
        
    except SyntaxError as e:
        log(f"Build script syntax error: {e}", "ERROR")
        return False
    except Exception as e:
        log(f"Build script import error: {e}", "ERROR")
        return False

def test_disk_space():
    """Test available disk space."""
    log("Testing available disk space...")
    
    try:
        import shutil
        disk_usage = shutil.disk_usage(Path(__file__).parent)
        free_space_gb = disk_usage.free / (1024**3)
        
        if free_space_gb < 1.0:
            log(f"Low disk space: {free_space_gb:.1f}GB available", "WARNING")
            log("At least 1GB recommended for building", "WARNING")
        else:
            log(f"Available disk space: {free_space_gb:.1f}GB ✓")
        
        return True
        
    except Exception as e:
        log(f"Could not check disk space: {e}", "WARNING")
        return True  # Not critical

def run_all_tests(verbose=False):
    """Run all tests."""
    log("Starting WOW Bingo Game Build System Tests")
    log("=" * 50)
    
    tests = [
        ("Python Environment", test_python_environment),
        ("Required Packages", test_required_packages),
        ("Nuitka Functionality", test_nuitka_functionality),
        ("Project Structure", test_project_structure),
        ("Build Script Syntax", test_build_script_syntax),
        ("Disk Space", test_disk_space)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        log(f"\nRunning test: {test_name}")
        log("-" * 30)
        
        try:
            if test_func():
                log(f"Test '{test_name}' PASSED ✓", "SUCCESS")
                passed += 1
            else:
                log(f"Test '{test_name}' FAILED ✗", "ERROR")
                failed += 1
        except Exception as e:
            log(f"Test '{test_name}' ERROR: {e}", "ERROR")
            failed += 1
    
    log("\n" + "=" * 50)
    log("TEST RESULTS")
    log("=" * 50)
    log(f"Passed: {passed}")
    log(f"Failed: {failed}")
    log(f"Total:  {passed + failed}")
    
    if failed == 0:
        log("\nAll tests passed! Build system is ready to use. ✓", "SUCCESS")
        log("\nNext steps:")
        log("1. Run: python nuitka_comprehensive_build.py")
        log("2. Or use the interactive scripts:")
        log("   Windows: build_comprehensive.bat")
        log("   Linux/macOS: ./build_comprehensive.sh")
        return True
    else:
        log(f"\n{failed} test(s) failed. Please fix the issues before building.", "ERROR")
        if failed == 1 and "Required Packages" in [t[0] for t in tests]:
            log("Tip: Run 'python setup_build_environment.py' to install missing packages", "INFO")
        return False

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Test WOW Bingo Game build system"
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    try:
        success = run_all_tests(args.verbose)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log("\nTests interrupted by user", "WARNING")
        sys.exit(1)

if __name__ == "__main__":
    main()
